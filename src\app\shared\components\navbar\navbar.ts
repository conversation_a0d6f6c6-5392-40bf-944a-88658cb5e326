import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { DirectionService } from '../../../core/services/direction.service';
import { SvgIcon } from '../svg-icon/svg-icon';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
interface MegaMenuSection {
  title: string;
  links: { label: string; href: string }[];
}

interface NavigationRoute {
  path: string;
  labelKey: string;
  label: string;
}
@Component({
  selector: 'sdga-navbar',
  imports: [SvgIcon, CommonModule, RouterModule, TranslateModule],
  templateUrl: './navbar.html',
  styleUrl: './navbar.scss',
})
export class Navbar {
  // Navigation routes based on app.routes.ts
  navigationRoutes: NavigationRoute[] = [
    { path: '/examples/tailwind-calsses', labelKey: 'EXAMPLES.TAILWIND_CLASSES', label: 'Tailwind Classes' },
    { path: '/examples/svg-icons', labelKey: 'EXAMPLES.SVG_ICONS', label: 'SVG Icons' },
    { path: '/examples/progress-indicators', labelKey: 'EXAMPLES.PROGRESS_INDICATORS', label: 'Progress Indicators' },
    { path: '/examples/inputs', labelKey: 'EXAMPLES.INPUTS', label: 'Inputs' },
    { path: '/examples/accordion', labelKey: 'EXAMPLES.ACCORDION', label: 'Accordion' },
    { path: '/examples/notifications', labelKey: 'EXAMPLES.NOTIFICATIONS', label: 'Notifications' },
    { path: '/examples/rating', labelKey: 'EXAMPLES.RATING', label: 'Rating' },
    { path: '/examples/modal', labelKey: 'EXAMPLES.MODAL', label: 'Modal' },
    { path: '/examples/quote', labelKey: 'EXAMPLES.QUOTE', label: 'Quote' },
    { path: '/examples/carousel', labelKey: 'EXAMPLES.CAROUSEL', label: 'Carousel' },
    { path: '/examples/buttons', labelKey: 'EXAMPLES.BUTTONS', label: 'Buttons' },
    { path: '/examples/content-switcher', labelKey: 'EXAMPLES.CONTENT_SWITCHER', label: 'Content Switcher' },
    { path: '/examples/tages', labelKey: 'EXAMPLES.TAGES', label: 'Tags' },
    { path: '/examples/menu', labelKey: 'EXAMPLES.MENU', label: 'Menu' },
    { path: '/examples/progress-bar', labelKey: 'EXAMPLES.PROGRESS_BAR', label: 'Progress Bar' },
    { path: '/examples/tree-example', labelKey: 'EXAMPLES.TREE_EXAMPLE', label: 'Tree Example' },
    { path: '/examples/code-snippet', labelKey: 'EXAMPLES.CODE_SNIPPET', label: 'Code Snippet' },
    { path: '/examples/table-example', labelKey: 'EXAMPLES.TABLE_EXAMPLE', label: 'Table Example' }
  ];

  pagesRoutes: NavigationRoute[] = [
    { path: '/contact-us', labelKey: 'CONTACT_US.TITLE', label: 'Contact Us' },
    { path: '/not-found', labelKey: 'NOT_FOUND.TITLE', label: 'Not Found' }
  ];

  megaMenu: MegaMenuSection[] = [
    {
      title: 'Section 1',
      links: [
        { label: 'Sub Link 1', href: '#' },
        { label: 'Sub Link 2', href: '#' },
        { label: 'Sub Link 3', href: '#' },
      ],
    },
    {
      title: 'Section 2',
      links: [
        { label: 'Sub Link A', href: '#' },
        { label: 'Sub Link B', href: '#' },
      ],
    },
    {
      title: 'More',
      links: [
        { label: 'Docs', href: '#' },
        { label: 'Support', href: '#' },
      ],
    },
  ];
  dropdownOpen = false;
  pagesDropdownOpen = false;
  lang: string | undefined;
  showMobileLinks = false;
  showMobileActions = false;
  constructor(
    private translate: TranslateService,
    private dir: DirectionService
  ) {
    this.lang = this.translate.currentLang;
  }

  switchLang(lang: 'en' | 'ar') {
    this.translate.use(lang);
    this.dir.setDirection(lang);
    localStorage.setItem('lang', lang);
  }

  @ViewChild('dropdown', { static: false }) dropdownRef!: ElementRef;

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    if (
      this.dropdownOpen &&
      this.dropdownRef &&
      !this.dropdownRef.nativeElement.contains(event.target)
    ) {
      this.dropdownOpen = false;
    }
  }

  toggleMobileLinks() {
    this.showMobileLinks = !this.showMobileLinks;
    this.showMobileActions = false; // Close other menu
  }

  toggleMobileActions() {
    this.showMobileActions = !this.showMobileActions;
    this.showMobileLinks = false; // Close other menu
  }

  getRouteLabel(route: NavigationRoute): string {
    return this.translate.instant(route.labelKey) || route.label;
  }
}
