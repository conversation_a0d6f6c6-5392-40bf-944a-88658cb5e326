import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';

// Avatar data interface for individual avatars
export interface AvatarData {
  src?: string;
  alt?: string;
  initials?: string;
  icon?: string;
  backgroundColor?: string;
  name?: string; // For accessibility
}

// Avatar size type
export type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

// Avatar shape type
export type AvatarShape = 'circle' | 'square';

@Component({
  selector: 'sdga-avatar',
  imports: [CommonModule, SvgIcon],
  templateUrl: './avatar.html',
  styleUrl: './avatar.scss'
})
export class Avatar implements OnInit {
  // Single avatar properties
  @Input() size: AvatarSize = 'md';
  @Input() shape: AvatarShape = 'circle';
  @Input() src?: string;
  @Input() alt?: string;
  @Input() initials?: string;
  @Input() icon?: string;
  @Input() backgroundColor?: string;
  @Input() name?: string; // For accessibility

  // Avatar group properties
  @Input() avatars?: AvatarData[];
  @Input() maxVisible: number = 3;
  @Input() isGroup: boolean = false;

  // Internal state
  imageError: boolean = false;
  displayedAvatars: AvatarData[] = [];
  remainingCount: number = 0;
  fullPathImg: string | undefined;

  ngOnInit() {
    if (this.avatars && this.avatars.length > 0) {
      this.isGroup = true;
      this.setupAvatarGroup();
    }

    if(this.src){
      this.fullPathImg = '../../../../assets/images/' + this.src;
    }
  }

  private setupAvatarGroup() {
    if (!this.avatars) return;

    this.displayedAvatars = this.avatars.slice(0, this.maxVisible);
    this.remainingCount = Math.max(0, this.avatars.length - this.maxVisible);
  }

  // Handle image loading errors
  onImageError() {
    this.imageError = true;
  }

  // Get size classes for avatar
  // getSizeClasses(): string {
  //   const sizeMap = {
  //     xs: 'w-3xl h-3xl text-text-2xs',
  //     sm: 'w-4xl h-4xl text-text-xs',
  //     md: 'w-5xl h-5xl text-text-sm',
  //     lg: 'w-6xl h-6xl text-text-md',
  //     xl: 'w-7xl h-7xl text-text-xl',
  //     '2xl': 'w-8xl h-8xl text-display-sm',
  //     '3xl': 'w-9.5xl h-9.5xl text-display-md'
  //   };
  //   return sizeMap[this.size];
  // }

  sizeClasses = {
    xs: 'w-3xl h-3xl text-text-2xs border-2 font-bold',
    sm: 'w-4xl h-4xl text-text-xs border-2 font-semibold',
    md: 'w-5xl h-5xl text-text-sm border-2 font-semibold',
    lg: 'w-6xl h-6xl text-text-md border-2 font-medium',
    xl: 'w-7xl h-7xl text-text-xl border-2 font-medium',
    '2xl': 'w-8xl h-8xl text-display-sm border-2 font-normal',
    '3xl': 'w-9.5xl h-9.5xl text-display-md border-4 font-normal'
  };

  // Get shape classes for avatar
  // getShapeClasses(): string {
  //   return this.shape === 'circle' ? 'rounded-full' : 'rounded-md';
  // }
  shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-md'
  };

  // Get background color for initials
  getBackgroundColor(): string {
    if (this.backgroundColor) {
      return this.backgroundColor;
    }

    // Default color palette for initials
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ];

    // Generate color based on initials or name
    const text = this.initials || this.name || '';
    const index = text.length > 0 ? text.charCodeAt(0) % colors.length : 0;
    return colors[index];
  }

  // Get initials from name if not provided
  getDisplayInitials(): string {
    if (this.initials) {
      return this.initials.toUpperCase();
    }

    if (this.name) {
      return this.name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }

    return 'User';
  }

  // Check if should show image
  shouldShowImage(): boolean {
    return !!(this.src && !this.imageError);
  }

  // Check if should show initials
  shouldShowInitials(): boolean {
    return !this.shouldShowImage() && !this.icon;
  }

  // Check if should show icon
  shouldShowIcon(): boolean {
    return !!(this.icon && !this.shouldShowImage());
  }

  // Get group avatar background color
  getGroupAvatarBackground(avatar: AvatarData, index: number): string {
    if (avatar.backgroundColor) {
      return avatar.backgroundColor;
    }

    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ];

    const text = avatar.initials || avatar.name || '';
    const colorIndex = text.length > 0 ? text.charCodeAt(0) % colors.length : index % colors.length;
    return colors[colorIndex];
  }

  // Get group avatar initials
  getGroupAvatarInitials(avatar: AvatarData): string {
    if (avatar.initials) {
      return avatar.initials.toUpperCase();
    }

    if (avatar.name) {
      return avatar.name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }

    return 'U';
  }

  // Handle group avatar image errors
  onGroupImageError(index: number) {
    if (this.displayedAvatars[index]) {
      this.displayedAvatars[index].src = undefined;
    }
  }

  // Track by function for ngFor
  trackByIndex(index: number, _item: AvatarData): number {
    return index;
  }
}
