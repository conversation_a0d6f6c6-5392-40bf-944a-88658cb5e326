import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON>, FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FormInput } from "../../shared/components/inputs/form-input/form-input";
import { FromDropdown } from "../../shared/components/inputs/from-dropdown/from-dropdown";
import { ImgUpload } from "../../shared/components/inputs/img-upload/img-upload";
import { SvgIcon } from "../../shared/components/svg-icon/svg-icon";

@Component({
  selector: 'sdga-contact-us',
  imports: [CommonModule, TranslateModule, ReactiveFormsModule, FormInput, FromDropdown, ImgUpload, SvgIcon],
  templateUrl: './contact-us.html',
  styleUrl: './contact-us.scss'
})
export class ContactUs {
formSubmitted = false;
contactForm: ReturnType<FormBuilder['group']>;

constructor(private fb: FormBuilder) {
  this.contactForm = this.fb.group({
    firstName: ['', Validators.required],
    lastName: ['', Validators.required],
    email: ['', [Validators.required, Validators.email]],
    phone: ['', Validators.required],
    subject: [''],
    category: [''],
    message: [''],
    file: [null],
  });
}

categories = [
  { label: 'Suggestion', value: 'suggestion' },
  { label: 'Complaint', value: 'complaint' },
];

copyToClipboard(value: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(value);
  } else {
    const textarea = document.createElement('textarea');
    textarea.value = value;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
  }
}

}
