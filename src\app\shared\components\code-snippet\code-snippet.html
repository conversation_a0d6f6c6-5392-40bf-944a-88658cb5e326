<!-- outer frame ---------------------------------------------------------->
<div class="border border-gray-200 rounded-lg">

  <!-- tab‑bar ------------------------------------------------------------>
  <div class="flex items-center gap-4 px-2 text-sm font-medium text-gray-700 bg-white border border-b border-gray-200 rounded-t-lg rounded-b-none sm:px-4">

    <button
      *ngFor="let tab of tabs; first as isFirst"
      (click)="activeTab = tab; expanded = false"
      class="pb-2 -mb-px transition-all hover:text-green-700"
      [class.font-bold]="tab === activeTab"
      [class.border-green-700]="tab === activeTab"
      [class.border-b-2]="tab === activeTab">
      {{ tab }}
    </button>

    <button
      class="p-1 ml-auto sm:p-6 rtl:ml-0 rtl:mr-auto"
      (click)="copyToClipboard()"
      title="Copy">
      <sdga-svg-icon name="copy-code" ></sdga-svg-icon>
    </button>
  </div>

  <!-- code block --------------------------------------------------------->
  <div class="relative overflow-x-auto text-sm leading-relaxed bg-white rounded-t-none rounded-b-lg">
    <ng-container *ngIf="codeSnippets && codeSnippets[activeTab] as snippet">
      <ng-container *ngIf="editable; else readonlyBlock">
        <div class="flex">
          <code class="p-6 mr-6 font-bold text-right border-r border-gray-400 select-none rtl:border-r-0 rtl:border-l rtl:mr-0 rtl:ml-6 text-quarterary-500 bg-gray-50">
            <ng-container *ngFor="let line of lines(snippet); let i = index">
              <span class="block">{{ i + 1 }}</span>
            </ng-container>
          </code>
          <textarea #editor class="w-full p-6 min-h-[8rem] bg-white rounded resize-y focus:outline-none text-default-100 text-text-md"
            [value]="snippet || ''"
            (input)="onEdit(editor.value)"></textarea>
        </div>
      </ng-container>
      <ng-template #readonlyBlock>
        <pre class="whitespace-pre-wrap"><code>{{ snippet.slice(0, 1000) }}</code></pre>
      </ng-template>
      <!-- show‑more toggle -------------------------------------------------->
      <div *ngIf="lines(snippet).length > maxLines"
           class="px-4 pb-3 -mt-1 text-text-sm text-default-100">
        <button (click)="expanded = !expanded" class="flex items-center gap-1 hover:text-green-700 float-end">
          <span>{{ expanded ? ('CODE_SNIPPET.SHOW_LESS' | translate) : ('CODE_SNIPPET.SHOW_MORE' | translate) }}</span>
          <sdga-svg-icon *ngIf="expanded" name="arrow-up"></sdga-svg-icon>
          <sdga-svg-icon *ngIf="!expanded" name="arrow-down"></sdga-svg-icon>
        </button>
      </div>
    </ng-container>
    <ng-container *ngIf="!codeSnippets || !codeSnippets[activeTab]">
      <pre class="p-2 text-gray-400 whitespace-pre-wrap sm:p-6"><code>{{ 'CODE_SNIPPET.NO_CODE' | translate }}</code></pre>
    </ng-container>
  </div>
</div>
