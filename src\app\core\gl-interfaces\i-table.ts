// i-table-column.ts
import { ColumnTypeEnum } from '../enums/column-type-enum';

export interface StatusObject {
  label: string;
  color: string;
}

export interface ITableColumn {
  columnDef: string;
  header: string;
  headerKey?: string; // Translation key for header
  columnType: ColumnTypeEnum;
  cell: (element: any) => any;
  class?: string;
  isSortingBy?: boolean;
  displayMode?:ActionDisplayMode;
}
export interface TableActionEvent {
  action: string;
  row: any;
}
export interface SwitchToggleEvent {
  row: any;
  newValue: boolean;
}
export interface TextLinkClickEvent {
  row: any;
  columnDef: string;
}
export enum ActionDisplayMode {
  Flex = 'flex',
  Dropdown = 'dropdown',
}
