/** @type {import('tailwindcss').Config} */
const rtl = require('tailwindcss-rtl');
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  safelist: [
    "text-sa-25",
    "bg-sa-25",
    "border-sa-25",
    "text-gray-25",
    "bg-gray-25",
    // RTL-specific classes for all components
    "rtl:rounded-r-md",
    "rtl:rounded-l-none",
    "rtl:border-l-0",
    "rtl:border-r",
    "rtl:border-r-0",
    "rtl:border-l",
    "rtl:text-right",
    "rtl:text-left",
    "rtl:space-x-reverse",
    "rtl:ml-0",
    "rtl:mr-1",
    "rtl:flex-row-reverse",
    "ltr:flex-row",
    "ltr:ml-1",
    "rtl:mr-1",
    "rtl:order-1",
    "rtl:order-2",
    "rtl:order-3",
    "order-1",
    "order-2",
    // Search input RTL classes
    "ltr:left-3",
    "rtl:right-3",
    "ltr:right-3",
    "rtl:left-3",
    "ltr:pl-10",
    "ltr:pr-12",
    "ltr:pr-3",
    "ltr:pl-3",
    "rtl:pr-10",
    "rtl:pl-12",
    "rtl:pl-3",
    "rtl:pr-3",
    // Positioning classes for radio button inner circle
    "z-10",
    "top-1",
    "left-1",
    // Tooltip classes
    "tooltip",
    "tooltip-light",
    "tooltip-dark",
    "tooltip-arrow",
    "tooltip-arrow-top",
    "tooltip-arrow-bottom",
    "tooltip-arrow-left",
    "tooltip-arrow-right",
    "tooltip-content",
    "tooltip-title",
    "tooltip-description",
    // Tooltip positioning and sizing
    "max-w-60", // 240px max width
    "w-60",
    "z-50",
    "absolute",
    "invisible",
    "opacity-0",
    "visible",
    "opacity-100",
    "transition-opacity",
    "duration-200",
    "ease-in-out",
    // Arrow positioning classes
    "before:absolute",
    "before:w-0",
    "before:h-0",
    "before:border-4",
    "before:border-transparent",
    "top-1.5",
    "left-1.5",
    "top-1.75",
    "left-1.75",
    // Switch component sizes
    "w-10", "h-5", "w-12", "h-6", "w-14", "h-7",
    "w-4", "h-4", "w-5", "h-5", "w-6", "h-6",
    "translate-x-5", "translate-x-6", "translate-x-7",
    // Error states
    "bg-red-50", "border-error-500", "text-red-600",
    // Responsive classes
    "sm:gap-2", "sm:grid-cols-2",
    // Animation classes
    "animate-shake",
    // Pagination component classes
    "min-w-[32px]", "min-w-[40px]", "min-w-[48px]",
    "h-8", "h-10", "h-12",
    "border-sa-500", "border-sa-600",
    "bg-sa-500", "bg-sa-600",
    "hover:bg-sa-600", "hover:border-sa-600",
    // Removed focus ring classes as per user request
    "rtl:rotate-180", "ltr:rotate-0", "ltr:rotate-180",
    "bg-transparent", "border-0", "relative",
    "absolute", "bottom-0", "left-1/2", "transform", "-translate-x-1/2",
    "w-6", "h-0.5", "rounded-full",
    // Dropdown classes
    "z-50", "mt-1", "shadow-lg", "max-h-48", "overflow-y-auto",
    "left-0", "right-0", "py-1", "w-full", "text-left",
    "hover:bg-gray-100", "hover:text-gray-900", "transition-colors",
    "duration-150", "justify-between", "items-center",
    // Active page indicator classes
    "bg-sa-50", "text-sa-700", "text-gray-900",
    "ltr:flex-row", "rtl:flex-row-reverse", "ltr:text-left", "rtl:text-right",
    "ltr:mr-2", "rtl:ml-2", "ltr:ml-auto", "rtl:mr-auto",
  ],
  theme: {
          container: {
        center: true,
        padding: {
          DEFAULT: "1rem", // Mobile
          sm: "1rem",
          md: "2rem", // Tablet
          lg: "2rem", // Desktop
          xl: "2rem",
          "2xl": "2rem",
        },
        screens: {
           sm: "640px",
            md: "768px",
            lg: "1024px",
            xl: "1280px",
            "2xl": "1536px",
        },
      },
    extend: {
      colors: {
        gray: {
          25: "#FCFCFD",
          50: "#F9FAFB",
          100: "#F3F4F6",
          200: "#E5E7EB",
          300: "#D2D6DB",
          400: "#9DA4AE",
          500: "#6C737F",
          600: "#4D5761",
          700: "#384250",
          800: "#1F2A37",
          900: "#111927",
          950: "#0D121C",
          "default":"#CBD5E1"
        },
        sa: {
          25: "#F7FDF9",
          50: "#F3FCF6",
          100: "#DFF6E7",
          200: "#88EACB",
          300: "#80D8AD",
          400: "#54C08A",
          500: "#25935F",
          600: "#1B8354",
          700: "#166A45",
          800: "#14573A",
          900: "#104631",
          950: "#092A1E",
          "lighter-100":"#88D8AD",
          "primary-200":"#B8EACB"
        },
        gold: {
          25: "#FFFEF7",
          50: "#FFFEF2",
          100: "#FFFCE6",
          200: "#FCF3BD",
          300: "#FAE996",
          400: "#F7D54D",
          500: "#F5BD02",
          600: "#DBA102",
          700: "#B87B02",
          800: "#945C01",
          900: "#6E3C00",
          950: "#472400",
        },
        lavender: {
          25: "#FEFCFF",
          50: "#F9F5FA",
          100: "#F2E9F5",
          200: "#E1CCE8",
          300: "#CCADD9",
          400: "#A57BBA",
          500: "#80519F",
          600: "#6D428F",
          700: "#532D75",
          800: "#3D1D5E",
          900: "#281047",
          950: "#16072E",
        },
        error: {
          25: "#FFFBFA",
          50: "#FEF3F2",
          100: "#FEE4E2",
          200: "#FECDCA",
          300: "#FDA29B",
          400: "#F97066",
          500: "#F04438",
          600: "#D92D20",
          700: "#B42318",
          800: "#912018",
          900: "#7A271A",
          950: "#55160C",
        },

        warning: {
          25: "#FFFCF5",
          50: "#FFFAEB",
          100: "#FEF0C7",
          200: "#FEDF89",
          300: "#FEC84B",
          400: "#FDB022",
          500: "#F79009",
          600: "#DC6803",
          700: "#B54708",
          800: "#93370D",
          900: "#7A2E0E",
          950: "#4E1D09",
        },
        info: {
          25: "#F5FAFF",
          50: "#ECFDF3",
          75: "#EFF8FF",
          100: "#D1E9FF",
          200: "#B2DDFF",
          300: "#84CAFF",
          400: "#53B1FD",
          500: "#2E90FA",
          600: "#1570EF",
          700: "#175CD3",
          800: "#1849A9",
          900: "#194185",
          950: "#102A56",
          "light" : "#EFF8FF"
        },
        success: {
          25: "#F6FEF9",
          50: "#ECFDF3",
          100: "#DCFAE6",
          200: "#ABEFC6",
          300: "#75E0A7",
          400: "#47CD89",
          500: "#17B26A",
          600: "#079455",
          700: "#067647",
          800: "#085D3A",
          900: "#074D31",
          950: "#053321",
        },
        default :{
          100: "#161616",
          'white-30': 'rgba(255,255,255,0.3)',
          "quarterary-500": "#667085",
        }
      },
      backgroundImage: {
        "gradient-sa-01": "linear-gradient(90deg,  #1B8354 0%, #25935F 100%)",
        "gradient-sa-02": "linear-gradient(45deg,  #166A45 0%, #1B8354 100%)",
        "gradient-sa-03": "linear-gradient(45deg,  #092A1E 0%, #1B8354 100%)",
        "gradient-sa-04": "linear-gradient(90deg,  #14573A 0%, #1B8354 100%)",
        "gradient-sa-05": "linear-gradient(26.5deg, #14573A 0%, #166A45 100%)",
        "gradient-sa-06": "linear-gradient(45deg,  #104631 0%, #1B8354 100%)",
        "gradient-grey-01":"linear-gradient(90deg,  rgba(157, 164, 174, 0.20) 0%, rgba(210, 214, 219, 0.20) 50%, rgba(229, 231, 235, 0.20) 100%)",
        "gradient-grey-01-arabic":"linear-gradient(90deg, rgba(229, 231, 235, 0.20) 0%, rgba(210, 214, 219, 0.20) 50%, rgba(157, 164, 174, 0.20)100%)"
      },textColor: {
        // Light BG
        "primary-light": "#0D121C",
        "secondary-light": "#384250",
        "tertiary-light": "#4D5761",

        // Dark BG (with alpha)
        "primary-dark": "rgba(255, 255, 255, 1)",
        "secondary-dark": "rgba(255, 255, 255, 0.7)",
        "tertiary-dark": "rgba(255, 255, 255, 0.6)",

        // Platforms Code
        "sa-primary": "#14573A",
        "sa-secondary": "#1B8354",
        "sa-tertiary": "#25935F",

        // Semantic
        "error-primary": "#D92D20",
        "warning-primary": "#DC6803",
        "success-primary": "#1B8354",
        "info-primary": "#1570EF",
      },
      fontFamily: {
        arabic: ["IBMPlexSansArabic", "sans-serif"],
      },
      fontSize: {
        // Display
        "display-2xl": [
          "4.5rem",
          { lineHeight: "5.625rem", letterSpacing: "-0.02em" },
        ],
        "display-xl": [
          "3.75rem",
          { lineHeight: "4.5rem", letterSpacing: "-0.02em" },
        ],
        "display-lg": [
          "3rem",
          { lineHeight: "3.75rem", letterSpacing: "-0.02em" },
        ],
        "display-md": [
          "2.25rem",
          { lineHeight: "2.75rem", letterSpacing: "-0.02em" },
        ],
        "display-sm": ["1.875rem", { lineHeight: "2.375rem" }],
        "display-xs": ["1.5rem", { lineHeight: "2rem" }],

        // Text
        "text-xl": ["1.25rem", { lineHeight: "1.875rem" }],
        "text-lg": ["1.125rem", { lineHeight: "1.75rem" }],
        "text-md": ["1rem", { lineHeight: "1.5rem" }],
        "text-sm": ["0.875rem", { lineHeight: "1.25rem" }],
        "text-xs": ["0.75rem", { lineHeight: "1.125rem" }],
        "text-2xs": ["0.625rem", { lineHeight: "1rem" }],
      },
      boxShadow: {
        // Shadows
        "sa-xs":
          "0px 1px 2px 0px rgba(16, 24, 40, 0.05), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)",
        "sa-sm":
          "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 4px 8px -2px rgba(16, 24, 40, 0.10)",
        "sa-md":
          "0px 2px 4px -2px rgba(16, 24, 40, 0.06), 0px 12px 16px -6px rgba(16, 24, 40, 0.08)",
        "sa-lg":
          "0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 20px 24px -4px rgba(16, 24, 40, 0.08)",
        "sa-xl": "0px 8px 8px -4px rgba(16, 24, 40, 0.03)",
        "sa-2xl": "0px 24px 48px -12px rgba(16, 24, 40, 0.18)",
        "sa-3xl": "0px 32px 64px -12px rgba(16, 24, 40, 0.14)",
      },

      backdropBlur: {
        "sa-sm": "8px",
        "sa-md": "16px",
        "sa-lg": "24px",
        "sa-xl": "40px",
      },
      spacing: {
        // Custom spacing scale
        none: "0rem", // 0px
        xxs: "0.125rem", // 2px
        xs: "0.25rem", // 4px
        sm: "0.375rem", // 6px
        md: "0.5rem", // 8px
        lg: "0.75rem", // 12px
        xl: "1rem", // 16px
        "2xl": "1.25rem", // 20px
        "3xl": "1.5rem", // 24px
        "4xl": "2rem", // 32px
        "5xl": "2.5rem", // 40px
        "6xl": "3rem", // 48px
        "7xl": "4rem", // 64px
        "8xl": "5rem", // 80px
        "9xl": "6rem", // 96px
        "9.5xl": "7.5rem", // 120px
        "10xl": "8rem", // 128px
        "11xl": "10rem", // 160px

        // Grid gutters
        "gutter-mobile": "1rem", // 16px
        "gutter-tablet": "2rem", // 32px
        "gutter-desktop": "2rem", // 32px
      },

      maxWidth: {
        // Width tokens
        "width-xxs": "20rem", // 320px
        "width-xs": "24rem", // 384px
        "width-sm": "30rem", // 480px
        "width-md": "35rem", // 560px
        "width-lg": "40rem", // 640px
        "width-xl": "48rem", // 768px
        "width-2xl": "64rem", // 1024px
        "width-3xl": "80rem", // 1280px
        "width-4xl": "90rem", // 1440px
        "width-5xl": "100rem", // 1600px
        "width-6xl": "120rem", // 1920px

        // Container max -width in desktop
        // "container-desktop": "80rem", // 1280px

        // Paragraphs
        paragraph: "45rem", // 720px
      },
      padding: {
        "container-mobile": "1rem", // 16px
        "container-desktop": "2rem", // 32px
      },
      borderRadius: {
        none: "0rem", // 0px
        xs: "0.125rem", // 2px
        sm: "0.25rem", // 4px
        md: "0.5rem", // 8px
        lg: "1rem", // 16px
        xl: "1.5rem", // 24px
        full: "9999px", // fully rounded
      },

      maxWidth: {
        "container-mobile": "343px",
        "container-tablet": "704px",
        "container-desktop": "1216px",
      },
      animation: {
        'shake': 'shake 0.5s ease-in-out',
      },
      keyframes: {
        shake: {
          '0%, 100%': { transform: 'translateX(0)' },
          '25%': { transform: 'translateX(-5px)' },
          '75%': { transform: 'translateX(5px)' },
        },
      },
    },
    padding: (theme) => theme("spacing"),
    // margin: (theme) => theme("spacing"),
    gap: (theme) => theme("spacing"),
  },
  plugins: [rtl],
};
