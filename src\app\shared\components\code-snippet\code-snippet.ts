import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import * as Prism from 'prismjs';
import { TranslateModule } from '@ngx-translate/core';

// optional: load more languages if needed
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-markup';
import { SvgIcon } from "../svg-icon/svg-icon";
@Component({
  selector: 'sdga-code-snippet',
  imports: [CommonModule, TranslateModule, SvgIcon],
  templateUrl: './code-snippet.html',
  styleUrl: './code-snippet.scss'
})
export class CodeSnippet {
  @Input() tabs: string[] = [];                      // ['Java','Python',...]
  @Input() codeSnippets: Record<string, string> = {}; // { Java:`...`, Python:`...` }
  @Input() maxLines = 12;                            // collapsed height
  @Input() editable = false;
  @Output() codeChange = new EventEmitter<{ tab: string, code: string }>();

  activeTab = this.tabs[0] || '';
  expanded  = false;

  constructor(private host: ElementRef) {}

  ngAfterViewInit() {
    Prism.highlightAllUnder(this.host.nativeElement);
  }

  /* helpers */
  langKey(tab: string): string {
    const t = tab.toLowerCase();
    if (t === 'c++')  return 'cpp';
    if (t === 'html') return 'markup';
    return t;
  }
  lines(code: string) { return code?.split('\n'); }

  copyToClipboard() {
    const text = this.codeSnippets[this.activeTab];
    navigator.clipboard.writeText(text).then(() => {
      // Optional: show a toast or change button icon temporarily
      console.log('Copied to clipboard');
    }).catch(err => {
      console.error('Copy failed', err);
    });
  }

  onEdit(code: string) {
    this.codeSnippets = { ...this.codeSnippets, [this.activeTab]: code };
    this.codeChange.emit({ tab: this.activeTab, code });
  }
}
