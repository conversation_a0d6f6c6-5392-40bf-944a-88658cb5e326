{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"sdga-theme": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "sdga", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss", "node_modules/prismjs/themes/prism.css", "node_modules/prismjs/themes/prism-tomorrow.css", "node_modules/prismjs/plugins/line-numbers/prism-line-numbers.css", "node_modules/prismjs/plugins/toolbar/prism-toolbar.css"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/plugins/line-numbers/prism-line-numbers.js", "node_modules/prismjs/plugins/toolbar/prism-toolbar.js", "node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.js", "node_modules/prismjs/components/prism-typescript.min.js", "node_modules/prismjs/components/prism-python.min.js", "node_modules/prismjs/components/prism-java.min.js", "node_modules/prismjs/components/prism-c.min.js", "node_modules/prismjs/components/prism-cpp.min.js", "node_modules/prismjs/components/prism-markup.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "sdga-theme:build:production"}, "development": {"buildTarget": "sdga-theme:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"]}}}}}, "cli": {"analytics": false}}