<ng-container *ngIf="dataSource">
  <div class="w-full bg-white">
    <table class="w-full border-collapse">
      <!-- Table Header -->
      <thead>
        <tr class="bg-gray-100 border-t border-b border-gray-300">
          <th *ngFor="let column of columns; let i = index"
              class="px-4 py-3 text-sm font-medium text-left text-gray-700"
              [class]="column.class || ''"
              [ngClass]="{
                'border-e border-gray-300': column.columnType !== columnTypeEnum.Actions
              }">
            <ng-container [ngSwitch]="column.columnType">
              <!-- Checkbox Header -->
              <ng-container *ngSwitchCase="columnTypeEnum.Checkbox">
                <sdga-form-input
                  type="checkbox"
                  [control]="selectAllControl"
                  [value]="selection.hasValue() && isAllSelected()"
                  (valueChanged)="onToggleAllRows()"
                  checkboxSize="sm"
                  class="border-0"
                ></sdga-form-input>
              </ng-container>

              <!-- Regular Headers -->
              <ng-container *ngSwitchDefault>
                <div class="flex items-center justify-start w-full">
                  <div class="flex items-center">
                    <!-- Actions Column Header - Show Filter Icon Only -->
                    <ng-container *ngIf="column.columnType === columnTypeEnum.Actions; else normalHeader">
                      <sdga-svg-icon name="fflter" class="w-4 h-4 text-black"></sdga-svg-icon>
                    </ng-container>

                    <!-- Normal Headers -->
                    <ng-template #normalHeader>
                      <span *ngIf="column.isSortingBy; else regularHeader"
                            class="flex items-start cursor-pointer hover:text-gray-900"
                            (click)="onSort(column.columnDef)">
                        {{ getColumnHeader(column) }}
                        <sdga-svg-icon name="arrow-up" class="w-3 h-3 ml-1 rtl:mr-1 rtl:ml-0 "></sdga-svg-icon>
                      </span>
                      <ng-template #regularHeader>
                        <span>{{ getColumnHeader(column) }}</span>
                      </ng-template>
                    </ng-template>
                  </div>

                  <!-- Filter Icon for Non-Actions Columns -->
                
                </div>
              </ng-container>
            </ng-container>
          </th>
        </tr>
      </thead>

      <!-- Table Body -->
      <tbody class="bg-white">
        <tr *ngFor="let row of dataSource.data; trackBy: trackByFn"
            class="border-b border-gray-100 hover:bg-gray-50">
          <td *ngFor="let column of columns; let i = index"
              class="px-4 py-3 text-sm text-gray-900"
              [class]="column.class || ''"
              [ngClass]="{'border-e border-gray-300': i === 0}">
            <ng-container [ngSwitch]="column.columnType">

              <!-- Checkbox Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Checkbox">
                <sdga-form-input
                  type="checkbox"
                  [control]="getRowCheckboxControl(row)"
                  [value]="selection.isSelected(row)"
                  (valueChanged)="onToggleRow(row)"
                  (click)="$event.stopPropagation()"
                  checkboxSize="sm"
                  class="border-0"
                ></sdga-form-input>
              </ng-container>

              <!-- Text Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Text">
                <span>{{ column.cell(row) }}</span>
              </ng-container>

              <!-- TextLink Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.TextLink">
                <a class="cursor-pointer text-sa-600 hover:text-sa-800"
                   (click)="onTextLinkClick(row, column.columnDef)">
                  {{ column.cell(row) }}
                </a>
              </ng-container>

              <!-- Status Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Status">
                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full"
                      [ngClass]="column.cell(row)?.class">
                  <span class="w-2 h-2 rounded-full mr-1.5"
                        [ngClass]="getStatusDotClass(column.cell(row))"></span>
                  {{ column.cell(row)?.label }}
                </span>
              </ng-container>

              <!-- Image Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Image">
                <img [src]="row.imgSrc"
                     [alt]="column.header"
                     class="object-cover w-8 h-8 rounded-full" />
              </ng-container>

              <!-- Switch Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Switch">
                <sdga-form-input
                  type="switch"
                  [control]="getSwitchControl(row, column.columnDef)"
                  [value]="column.cell(row)"
                  (valueChanged)="onToggle(row, $event)"
                  switchSize="md"
                ></sdga-form-input>
              </ng-container>

              <!-- Actions Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Actions">
                <ng-container *ngIf="column.displayMode === ActionDisplayMode.Dropdown">
                  <!-- Custom Actions Dropdown -->
                  <div class="relative inline-block">
                    <button
                      class="p-1 text-black hover:text-gray-600 focus:outline-none"
                      (click)="toggleActionDropdown(row)"
                      [attr.aria-label]="'Actions for row'"
                    >
                      <sdga-svg-icon name="actionsTable" class="w-4 h-4"></sdga-svg-icon>
                    </button>

                    <!-- Dropdown Menu -->
                    <div
                      *ngIf="isActionDropdownOpen(row)"
                      class="absolute right-0 z-50 mt-1 bg-white border border-gray-300 rounded-md shadow-lg min-w-32"
                    >
                      <div class="py-1">
                        <button
                          *ngFor="let button of column.cell(row)?.buttons"
                          class="block w-full px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                          [ngClass]="button.class"
                          (click)="handleActionClick(button.action, row)"
                        >
                          {{ button.label }}
                        </button>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <ng-container *ngIf="column.displayMode === ActionDisplayMode.Flex">
                  <!-- Flex Buttons -->
                  <div class="flex space-x-1">
                    <button
                      *ngFor="let button of column.cell(row)?.buttons"
                      class="px-2 py-1 text-xs font-medium rounded hover:bg-gray-100"
                      [ngClass]="button.class || 'text-gray-600 hover:text-gray-800'"
                      (click)="handleAction(button.action, row)"
                    >
                      {{ button.label }}
                    </button>
                  </div>
                </ng-container>
              </ng-container>

              <!-- Custom Column -->
              <ng-container *ngSwitchCase="columnTypeEnum.Custom">
                <ng-container
                  *ngTemplateOutlet="customColumnTemplate; context: { element: row }"
                ></ng-container>
              </ng-container>

            </ng-container>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div class="mt-4">
    <sdga-pagination
      [totalItems]="totalItems"
      [itemsPerPage]="pageSize"
      [currentPage]="currentPage"
      size="medium"
      (pageChange)="onSdgaPageChange($event)"
    ></sdga-pagination>
  </div>
</ng-container>
