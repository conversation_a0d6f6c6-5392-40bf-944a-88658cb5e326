import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Table } from '../../shared/components/table/table';
import { ColumnTypeEnum, DataHandlingType } from '../../core/enums/column-type-enum';
import { ActionDisplayMode, ITableColumn, TableActionEvent, SwitchToggleEvent, TextLinkClickEvent } from '../../core/gl-interfaces/i-table';
import { SelectionModel } from '@angular/cdk/collections';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

interface Employee {
  id: number;
  name: string;
  email: string;
  department: string;
  position: string;
  salary: number;
  status: 'active' | 'inactive' | 'pending';
  isManager: boolean;
  avatar: string;
  joinDate: Date;
}

@Component({
  selector: 'sdga-table-example',
  imports: [CommonModule, Table, TranslateModule],
  templateUrl: './table-example.html',
  styleUrl: './table-example.scss'
})
export class TableExample implements OnInit {
  // Table configuration
  columns: ITableColumn[] = [];
  displayedColumns: string[] = [];
  dataSource: any;
  selection = new SelectionModel<Employee>(true, []);

  // Pagination settings
  totalItems = 0;
  pageSize = 10;
  currentPage = 1;

  // Data handling types
  sortingType = DataHandlingType.Frontend;
  paginationType = DataHandlingType.Frontend;

  // Sample data
  employees: Employee[] = [];
  paginatedEmployees: Employee[] = [];

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.initializeData();
    this.setupColumns();
    this.setupDataSource();
    this.updatePaginatedData();
  }

  private initializeData(): void {
    this.employees = [
      {
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        department: 'تقنية المعلومات',
        position: 'مطور أول',
        salary: 15000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        joinDate: new Date('2022-01-15')
      },
      {
        id: 2,
        name: 'فاطمة علي',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'أخصائي موارد بشرية',
        salary: 12000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        joinDate: new Date('2022-03-20')
      },
      {
        id: 3,
        name: 'محمد السعيد',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'محاسب',
        salary: 10000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        joinDate: new Date('2023-06-10')
      },
      {
        id: 4,
        name: 'نورا خالد',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'مدير تسويق',
        salary: 18000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        joinDate: new Date('2021-09-05')
      },
      {
        id: 5,
        name: 'عبدالله أحمد',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'مندوب مبيعات',
        salary: 8000,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        joinDate: new Date('2023-01-12')
      },

      // HR Department
      {
        id: 6,
        name: 'فاطمة علي',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'مدير موارد بشرية',
        salary: 18000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        joinDate: new Date('2020-05-15')
      },
      {
        id: 7,
        name: 'خالد محمود',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'أخصائي توظيف',
        salary: 9000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150',
        joinDate: new Date('2022-08-20')
      },
      {
        id: 8,
        name: 'مريم عبدالله',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'أخصائي تدريب',
        salary: 8500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
        joinDate: new Date('2023-02-10')
      },
      {
        id: 9,
        name: 'يوسف الأحمد',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'أخصائي رواتب',
        salary: 9500,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150',
        joinDate: new Date('2021-11-05')
      },
      {
        id: 10,
        name: 'هند الزهراني',
        email: '<EMAIL>',
        department: 'الموارد البشرية',
        position: 'أخصائي علاقات موظفين',
        salary: 8000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
        joinDate: new Date('2023-07-01')
      },

      // Finance Department
      {
        id: 11,
        name: 'عمر الحربي',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'مدير مالي',
        salary: 20000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        joinDate: new Date('2019-03-10')
      },
      {
        id: 12,
        name: 'أسماء الغامدي',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'محاسب أول',
        salary: 12000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        joinDate: new Date('2021-07-15')
      },
      {
        id: 13,
        name: 'سعد العتيبي',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'محاسب',
        salary: 10000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        joinDate: new Date('2022-11-20')
      },
      {
        id: 14,
        name: 'ريم الشهري',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'محلل مالي',
        salary: 11000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        joinDate: new Date('2023-04-05')
      },
      {
        id: 15,
        name: 'طارق الدوسري',
        email: '<EMAIL>',
        department: 'المالية',
        position: 'مراجع حسابات',
        salary: 9500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        joinDate: new Date('2022-09-12')
      },

      // Marketing Department
      {
        id: 16,
        name: 'نورا خالد',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'مدير تسويق',
        salary: 18000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        joinDate: new Date('2021-09-05')
      },
      {
        id: 17,
        name: 'محمد الشمري',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'أخصائي تسويق رقمي',
        salary: 10000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150',
        joinDate: new Date('2022-12-01')
      },
      {
        id: 18,
        name: 'لينا القحطاني',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'مصمم جرافيك',
        salary: 8500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
        joinDate: new Date('2023-01-20')
      },
      {
        id: 19,
        name: 'فهد الرشيد',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'منسق فعاليات',
        salary: 7500,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150',
        joinDate: new Date('2022-06-15')
      },
      {
        id: 20,
        name: 'دانا العنزي',
        email: '<EMAIL>',
        department: 'التسويق',
        position: 'كاتب محتوى',
        salary: 8000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
        joinDate: new Date('2023-05-10')
      },

      // Sales Department
      {
        id: 21,
        name: 'راشد المطيري',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'مدير مبيعات',
        salary: 17000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        joinDate: new Date('2020-08-15')
      },
      {
        id: 22,
        name: 'جواهر الفيصل',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'مندوب مبيعات أول',
        salary: 9500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        joinDate: new Date('2021-12-10')
      },
      {
        id: 23,
        name: 'بندر الحارثي',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'مندوب مبيعات',
        salary: 8000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150',
        joinDate: new Date('2022-04-20')
      },
      {
        id: 24,
        name: 'شهد الزهراني',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'منسق مبيعات',
        salary: 7000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
        joinDate: new Date('2023-03-15')
      },
      {
        id: 25,
        name: 'عادل السبيعي',
        email: '<EMAIL>',
        department: 'المبيعات',
        position: 'أخصائي خدمة عملاء',
        salary: 6500,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150',
        joinDate: new Date('2022-10-05')
      },

      // Operations Department
      {
        id: 26,
        name: 'منى الخالدي',
        email: '<EMAIL>',
        department: 'العمليات',
        position: 'مدير عمليات',
        salary: 19000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
        joinDate: new Date('2019-11-20')
      },
      {
        id: 27,
        name: 'ماجد القرني',
        email: '<EMAIL>',
        department: 'العمليات',
        position: 'منسق عمليات',
        salary: 10000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        joinDate: new Date('2021-05-12')
      },
      {
        id: 28,
        name: 'رنا الشهراني',
        email: '<EMAIL>',
        department: 'العمليات',
        position: 'أخصائي جودة',
        salary: 9000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        joinDate: new Date('2022-02-28')
      },
      {
        id: 29,
        name: 'حسام العسيري',
        email: '<EMAIL>',
        department: 'العمليات',
        position: 'مشرف إنتاج',
        salary: 8500,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        joinDate: new Date('2023-06-01')
      },
      {
        id: 30,
        name: 'غادة المالكي',
        email: '<EMAIL>',
        department: 'العمليات',
        position: 'محلل عمليات',
        salary: 9500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        joinDate: new Date('2022-08-10')
      },

      // Legal Department
      {
        id: 31,
        name: 'عبدالعزيز الراجحي',
        email: '<EMAIL>',
        department: 'الشؤون القانونية',
        position: 'مستشار قانوني',
        salary: 22000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        joinDate: new Date('2018-09-15')
      },
      {
        id: 32,
        name: 'نوال الحمدان',
        email: '<EMAIL>',
        department: 'الشؤون القانونية',
        position: 'محامي',
        salary: 15000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        joinDate: new Date('2020-12-01')
      },
      {
        id: 33,
        name: 'سلطان العمري',
        email: '<EMAIL>',
        department: 'الشؤون القانونية',
        position: 'أخصائي عقود',
        salary: 12000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150',
        joinDate: new Date('2023-01-25')
      },

      // Customer Service Department
      {
        id: 34,
        name: 'أمل الثقفي',
        email: '<EMAIL>',
        department: 'خدمة العملاء',
        position: 'مدير خدمة عملاء',
        salary: 16000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
        joinDate: new Date('2020-06-10')
      },
      {
        id: 35,
        name: 'وليد الجهني',
        email: '<EMAIL>',
        department: 'خدمة العملاء',
        position: 'ممثل خدمة عملاء',
        salary: 7000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150',
        joinDate: new Date('2022-03-18')
      },
      {
        id: 36,
        name: 'رهف الدوسري',
        email: '<EMAIL>',
        department: 'خدمة العملاء',
        position: 'ممثل خدمة عملاء',
        salary: 6800,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
        joinDate: new Date('2022-07-22')
      },
      {
        id: 37,
        name: 'فيصل الشمري',
        email: '<EMAIL>',
        department: 'خدمة العملاء',
        position: 'مشرف خدمة عملاء',
        salary: 9000,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        joinDate: new Date('2021-11-30')
      },
      {
        id: 38,
        name: 'جميلة القرشي',
        email: '<EMAIL>',
        department: 'خدمة العملاء',
        position: 'أخصائي شكاوى',
        salary: 7500,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        joinDate: new Date('2023-04-12')
      },

      // Research & Development
      {
        id: 39,
        name: 'إبراهيم الغامدي',
        email: '<EMAIL>',
        department: 'البحث والتطوير',
        position: 'مدير بحث وتطوير',
        salary: 25000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        joinDate: new Date('2018-02-20')
      },
      {
        id: 40,
        name: 'رنيم الحربي',
        email: '<EMAIL>',
        department: 'البحث والتطوير',
        position: 'باحث أول',
        salary: 18000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        joinDate: new Date('2019-08-15')
      },
      {
        id: 41,
        name: 'زياد العتيبي',
        email: '<EMAIL>',
        department: 'البحث والتطوير',
        position: 'مطور منتجات',
        salary: 14000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        joinDate: new Date('2021-01-10')
      },
      {
        id: 42,
        name: 'لمى الشهري',
        email: '<EMAIL>',
        department: 'البحث والتطوير',
        position: 'محلل أبحاث',
        salary: 12000,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        joinDate: new Date('2023-02-28')
      },

      // Security Department
      {
        id: 43,
        name: 'نايف المطيري',
        email: '<EMAIL>',
        department: 'الأمن',
        position: 'مدير أمن',
        salary: 16000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150',
        joinDate: new Date('2019-05-12')
      },
      {
        id: 44,
        name: 'عبير الزهراني',
        email: '<EMAIL>',
        department: 'الأمن',
        position: 'أخصائي أمن معلومات',
        salary: 13000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
        joinDate: new Date('2021-09-20')
      },
      {
        id: 45,
        name: 'حمد الدوسري',
        email: '<EMAIL>',
        department: 'الأمن',
        position: 'حارس أمن',
        salary: 6000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150',
        joinDate: new Date('2022-12-15')
      },

      // Administration
      {
        id: 46,
        name: 'سعاد الحارثي',
        email: '<EMAIL>',
        department: 'الإدارة',
        position: 'مدير إداري',
        salary: 17000,
        status: 'active',
        isManager: true,
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150',
        joinDate: new Date('2019-10-08')
      },
      {
        id: 47,
        name: 'عثمان القحطاني',
        email: '<EMAIL>',
        department: 'الإدارة',
        position: 'سكرتير تنفيذي',
        salary: 8000,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        joinDate: new Date('2021-04-25')
      },
      {
        id: 48,
        name: 'نجلاء العنزي',
        email: '<EMAIL>',
        department: 'الإدارة',
        position: 'منسق إداري',
        salary: 7000,
        status: 'inactive',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        joinDate: new Date('2022-01-30')
      },
      {
        id: 49,
        name: 'صالح الشهراني',
        email: '<EMAIL>',
        department: 'الإدارة',
        position: 'مساعد إداري',
        salary: 6500,
        status: 'pending',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        joinDate: new Date('2023-03-20')
      },
      {
        id: 50,
        name: 'هيا المالكي',
        email: '<EMAIL>',
        department: 'الإدارة',
        position: 'استقبال',
        salary: 5500,
        status: 'active',
        isManager: false,
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        joinDate: new Date('2022-11-10')
      }
    ];

    this.totalItems = this.employees.length;
  }

  private setupColumns(): void {
    this.columns = [
      {
        columnDef: 'select',
        header: '',
        columnType: ColumnTypeEnum.Checkbox,
        cell: () => null,
        class: 'w-12'
      },
    
      {
        columnDef: 'name',
        header: 'الاسم',
        headerKey: 'table.headers.name',
        columnType: ColumnTypeEnum.Text,
        cell: (employee: Employee) => employee.name,
        isSortingBy: true,
        class: 'font-medium text-gray-900'
      },
      {
        columnDef: 'email',
        header: 'البريد الإلكتروني',
        headerKey: 'table.headers.email',
        columnType: ColumnTypeEnum.TextLink,
        cell: (employee: Employee) => employee.email,
        class: ''
      },
      {
        columnDef: 'department',
        header: 'القسم',
        headerKey: 'table.headers.department',
        columnType: ColumnTypeEnum.Text,
        cell: (employee: Employee) => employee.department,
        isSortingBy: true
      },
      {
        columnDef: 'position',
        header: 'المنصب',
        headerKey: 'table.headers.position',
        columnType: ColumnTypeEnum.Text,
        cell: (employee: Employee) => employee.position
      },
      {
        columnDef: 'salary',
        header: 'الراتب',
        headerKey: 'table.headers.salary',
        columnType: ColumnTypeEnum.Text,
        cell: (employee: Employee) => `${employee.salary.toLocaleString()} ريال`,
        isSortingBy: true,
      },
      {
        columnDef: 'status',
        header: 'الحالة',
        headerKey: 'table.headers.status',
        columnType: ColumnTypeEnum.Status,
        cell: (employee: Employee) => this.getStatusObject(employee.status)
      },
      {
        columnDef: 'isManager',
        header: 'مدير',
        headerKey: 'table.headers.manager',
        columnType: ColumnTypeEnum.Switch,
        cell: (employee: Employee) => employee.isManager
      },
      {
        columnDef: 'actions',
        header: '',
        headerKey: '',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Dropdown,
        cell: (_employee: Employee) => ({
          buttons: [
            { action: 'view', label: this.translateService.instant('table.actions.view'), class: 'text-blue-600' },
            { action: 'edit', label: this.translateService.instant('table.actions.edit'), class: 'text-green-600' },
            { action: 'delete', label: this.translateService.instant('table.actions.delete'), class: 'text-red-600' }
          ]
        }),
        class: 'w-20'
      }
    ];

    this.displayedColumns = this.columns.map(col => col.columnDef);
  }

  private setupDataSource(): void {
    this.dataSource = {
      data: this.paginatedEmployees
    };
  }

  private updatePaginatedData(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedEmployees = this.employees.slice(startIndex, endIndex);
    this.dataSource = {
      data: this.paginatedEmployees
    };

    console.log(`Pagination: Page ${this.currentPage}, showing ${this.paginatedEmployees.length} employees (${startIndex + 1}-${Math.min(endIndex, this.totalItems)} of ${this.totalItems})`);
  }

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.pageSize);
  }

  private getStatusObject(status: string): any {
    const statusMap = {
      'active': {
        label: this.translateService.instant('table.status.active'),
        class: 'bg-green-100 text-green-800'
      },
      'inactive': {
        label: this.translateService.instant('table.status.inactive'),
        class: 'bg-red-100 text-red-800'
      },
      'pending': {
        label: this.translateService.instant('table.status.pending'),
        class: 'bg-yellow-100 text-yellow-800'
      }
    };
    return statusMap[status as keyof typeof statusMap] || statusMap['pending'];
  }

  // Event handlers
  onTableAction(event: TableActionEvent): void {
    console.log('Table action:', event);
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewEmployee(row);
        break;
      case 'edit':
        this.editEmployee(row);
        break;
      case 'delete':
        this.deleteEmployee(row);
        break;
    }
  }

  onSwitchToggle(event: SwitchToggleEvent): void {
    console.log('Switch toggle:', event);
    const employee = event.row as Employee;
    employee.isManager = event.newValue;
  }

  onTextLinkClick(event: TextLinkClickEvent): void {
    console.log('Text link click:', event);
    if (event.columnDef === 'email') {
      window.open(`mailto:${event.row.email}`);
    }
  }

  onToggleAllRows(): void {
    if (this.selection.hasValue()) {
      this.selection.clear();
    } else {
      this.employees.forEach(employee => this.selection.select(employee));
    }
  }

  onToggleRow(row: Employee): void {
    this.selection.toggle(row);
  }

  onSortChanged(event: { active: string; direction: string }): void {
    console.log('Sort changed:', event);
    // Handle backend sorting if needed
  }

  onFilterClick(columnDef: string): void {
    console.log('Filter clicked for column:', columnDef);
    const message = this.translateService.instant('table.messages.filter_applied', { column: columnDef });
    alert(message);
  }

  onPageChanged(event: any): void {
    console.log('Page changed:', event);

    // Handle both SDGA PageChangeEvent (frontend) and Material UI format (backend)
    if (event.page) {
      // SDGA PageChangeEvent format
      this.currentPage = event.page;
    } else if (event.pageIndex !== undefined) {
      // Material UI format (0-based)
      this.currentPage = event.pageIndex + 1;
    }

    this.updatePaginatedData();
  }

  // Action methods
  private viewEmployee(employee: Employee): void {
    alert(`عرض تفاصيل الموظف: ${employee.name}`);
  }

  private editEmployee(employee: Employee): void {
    alert(`تعديل الموظف: ${employee.name}`);
  }

  private deleteEmployee(employee: Employee): void {
    if (confirm(`هل أنت متأكد من حذف الموظف: ${employee.name}؟`)) {
      const index = this.employees.findIndex(emp => emp.id === employee.id);
      if (index > -1) {
        this.employees.splice(index, 1);
        this.totalItems = this.employees.length;

        // Adjust current page if necessary
        const maxPage = Math.ceil(this.totalItems / this.pageSize);
        if (this.currentPage > maxPage && maxPage > 0) {
          this.currentPage = maxPage;
        }

        this.updatePaginatedData();
      }
    }
  }
}
