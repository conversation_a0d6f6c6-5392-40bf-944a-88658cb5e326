<div class="container mx-auto p-6 space-y-6">
  <!-- Page Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">مثال شامل على جدول SDGA</h1>
    <p class="text-gray-600">
      هذا المثال يوضح جميع ميزات مكون الجدول في نظام التصميم الحكومي السعودي الرقمي،
      بما في ذلك أنواع الأعمدة المختلفة، والترقيم، والترتيب، والإجراءات.
    </p>
  </div>

  <!-- Table Features Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <div class="bg-sa-50 border border-sa-200 rounded-lg p-4">
      <h3 class="font-semibold text-sa-800 mb-2">أنواع الأعمدة</h3>
      <ul class="text-sm text-sa-700 space-y-1">
        <li>• نص عادي</li>
        <li>• رابط نصي</li>
        <li>• صندوق اختيار</li>
        <li>• حالة ملونة</li>
        <li>• صورة</li>
        <li>• مفتاح تبديل</li>
        <li>• إجراءات</li>
      </ul>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="font-semibold text-blue-800 mb-2">الميزات التفاعلية</h3>
      <ul class="text-sm text-blue-700 space-y-1">
        <li>• ترتيب الأعمدة</li>
        <li>• تحديد متعدد</li>
        <li>• ترقيم الصفحات</li>
        <li>• قائمة إجراءات منسدلة</li>
      </ul>
    </div>

    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <h3 class="font-semibold text-green-800 mb-2">تصميم SDGA</h3>
      <ul class="text-sm text-green-700 space-y-1">
        <li>• ألوان النظام</li>
        <li>• خطوط متسقة</li>
        <li>• مساحات منتظمة</li>
        <li>• تأثيرات تفاعلية</li>
      </ul>
    </div>

    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <h3 class="font-semibold text-purple-800 mb-2">دعم RTL</h3>
      <ul class="text-sm text-purple-700 space-y-1">
        <li>• محاذاة صحيحة</li>
        <li>• ترتيب عكسي</li>
        <li>• أيقونات مناسبة</li>
        <li>• نصوص عربية</li>
      </ul>
    </div>
  </div>

  <!-- Table Controls -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <div class="flex flex-wrap items-center justify-between gap-4">
      <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <span class="text-sm font-medium text-gray-700">إجمالي الموظفين:</span>
        <span class="px-2 py-1 bg-sa-100 text-sa-800 rounded-full text-sm font-semibold">
          {{ totalItems }}
        </span>
      </div>

      <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <span class="text-sm font-medium text-gray-700">المحدد:</span>
        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
          {{ selection.selected.length }}
        </span>
      </div>

      <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <span class="text-sm font-medium text-gray-700">الصفحة الحالية:</span>
        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold">
          {{ currentPage }} من {{ totalPages }}
        </span>
      </div>
    </div>
  </div>

  <!-- SDGA Table Component -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">جدول الموظفين</h2>

    <sdga-table
      [columns]="columns"
      [displayedColumns]="displayedColumns"
      [dataSource]="dataSource"
      [selection]="selection"
      [totalItems]="totalItems"
      [pageSize]="pageSize"
      [currentPage]="currentPage"
      [sortingType]="sortingType"
      [paginationType]="paginationType"
      (onClickAction)="onTableAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (textLinkClick)="onTextLinkClick($event)"
      (toggleAllRows)="onToggleAllRows()"
      (toggleRow)="onToggleRow($event)"
      (sortChanged)="onSortChanged($event)"
      (pageChange)="onPageChanged($event)"
      (filterClick)="onFilterClick($event)"
    ></sdga-table>
  </div>

  <!-- Code Example -->
  <div class="bg-gray-50 rounded-lg border border-gray-200 p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">مثال على الكود</h3>
    <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
      <pre class="text-green-400 text-sm"><code>&lt;sdga-table
  [columns]="columns"
  [dataSource]="dataSource"
  [selection]="selection"
  [totalItems]="totalItems"
  [pageSize]="pageSize"
  [sortingType]="DataHandlingType.Frontend"
  [paginationType]="DataHandlingType.Frontend"
  (onClickAction)="onTableAction($event)"
  (switchToggleEvent)="onSwitchToggle($event)"
  (textLinkClick)="onTextLinkClick($event)"
&gt;&lt;/sdga-table&gt;</code></pre>
    </div>
  </div>
</div>
