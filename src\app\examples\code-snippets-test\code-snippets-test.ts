import { Component } from '@angular/core';
import { CodeSnippet } from "../../shared/components/code-snippet/code-snippet";

@Component({
  selector: 'sdga-code-snippets-test',
  imports: [CodeSnippet],
  templateUrl: './code-snippets-test.html',
  styleUrl: './code-snippets-test.scss'
})
export class CodeSnippetsTest {
  codeSnippets = {
    Java:   `// Java Example\npublic class HelloWorld {\n  public static void main(String[] args) {\n    System.out.println(\"Hello, World!\");\n  }\n}`,
    Python: `# Python Example\nprint('Hello, World!')`,
    C:      `// C Example\n#include <stdio.h>\nint main() {\n  printf(\"Hello, World!\\n\");\n  return 0;\n}`,
    'C++':  `// C++ Example\n#include <iostream>\nint main() {\n  std::cout << \"Hello, World!\\n\";\n  return 0;\n}`,
    HTML:   `<!-- HTML Example -->\n<h1>Hello, World!</h1>`,
    PHP:    `<?php\n// PHP Example\necho 'Hello, World!';\n?>`
  };

  onCodeChange(event: { tab: string, code: string }) {
    this.codeSnippets = { ...this.codeSnippets, [event.tab]: event.code };
  }
}
