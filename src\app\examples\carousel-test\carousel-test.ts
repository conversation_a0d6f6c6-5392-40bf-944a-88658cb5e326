import { Component } from '@angular/core';
import { Carousel } from '../../shared/components/carousel/carousel';
import { assetPath } from '../../core/services/utils';
@Component({
  selector: 'sdga-carousel-test',
  imports: [Carousel],
  templateUrl: './carousel-test.html',
  styleUrl: './carousel-test.scss',
})
export class CarouselTest {
  assetPath(filename: string): string {
    return `assets/${filename}`;
  }
  images = [
    assetPath('KSA1.jpg'),
    assetPath('KSA2.jpg'),
    assetPath('KSA3.jpg'),
  ];
}
