import { Component } from '@angular/core';
import { Avatar, AvatarData } from '../../shared/components/avatar/avatar';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'sdga-avatar-test',
  imports: [CommonModule, Avatar],
  templateUrl: './avatar-test.html',
  styleUrl: './avatar-test.scss'
})
export class AvatarTest {
  personImagePath: string = 'sky.jpg';

  // Sample avatar data for testing
  sampleAvatars: AvatarData[] = [
    {
      name: '<PERSON>',
      initials: 'J<PERSON>',
      backgroundColor: 'bg-blue-500'
    },
    {
      name: '<PERSON>',
      initials: 'J<PERSON>',
      backgroundColor: 'bg-green-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-purple-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-pink-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON>',
      backgroundColor: 'bg-yellow-500'
    },
    {
      name: '<PERSON>',
      initials: 'L<PERSON>',
      backgroundColor: 'bg-red-500'
    }
  ];

  largeAvatarGroup: AvatarData[] = [
    { name: '<PERSON>', initials: '<PERSON>' },
    { name: '<PERSON>', initials: '<PERSON><PERSON>' },
    { name: '<PERSON>', initials: '<PERSON>' },
    { name: '<PERSON> <PERSON>', initials: 'D<PERSON>' },
    { name: '<PERSON> <PERSON>', initials: '<PERSON><PERSON>' },
    { name: '<PERSON> <PERSON>', initials: '<PERSON>' },
    { name: '<PERSON> <PERSON>', initials: '<PERSON><PERSON>' },
    { name: '<PERSON> <PERSON>', initials: '<PERSON><PERSON>' }
  ];
}
