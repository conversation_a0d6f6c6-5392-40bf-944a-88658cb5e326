<button
  (click)="prev()"
  class="absolute top-1/2 left-3 rounded-full"
  [ngClass]="buttonStyle()"
>
  <sdga-svg-icon [name]="leftArrow" [svgClass]="iconClass"></sdga-svg-icon>
</button>
<button
  (click)="next()"
  class="absolute top-1/2 right-3 rounded-full"
  [ngClass]="buttonStyle()"
>
  <sdga-svg-icon [name]="rightArrow" [svgClass]="iconClass"></sdga-svg-icon>
</button>
<div class="relative w-[80%] h-64 mx-auto overflow-hidden">
  <img
    [src]="images[currentIndex]"
    alt="Carousel image"
    class="w-full h-full transition-all duration-500"
  />
  <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-2">
    <button
      *ngFor="let img of images; let i = index"
      (click)="goToSlide(i)"
      [ngClass]="{
        'bg-sa-600': i === currentIndex,
        'bg-gray-200': i !== currentIndex
      }"
      [ngStyle]="{
        width: dotSize + 'px',
        height: dotSize + 'px'
  }"
      class="w-3 h-3 rounded-full transition-all duration-300"
    ></button>
  </div>
</div>
