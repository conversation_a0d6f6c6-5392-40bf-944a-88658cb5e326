﻿{
  "GENERAL": {
    "NEXT": "Next",
    "BACK": "Back",
    "SUBMIT": "Submit",
    "RESET": "Reset",
    "CANCEL": "Cancel",
    "SAVE": "Save",
    "DELETE": "Delete",
    "EDIT": "Edit",
    "VIEW": "View",
    "CLOSE": "Close",
    "CONFIRM": "Confirm",
    "YES": "Yes",
    "NO": "No"
  },
  "APP": {
    "TITLE": "SDGA Theme",
    "DESCRIPTION": "Saudi Digital Government Authority Design System"
  },
  "FORM": {
    "IS_REQUIRED": "This field is required",
    "INVALID_EMAIL": "Please enter a valid email address",
    "INVALID_PASSWORD": "Password must be at least 8 characters",
    "PASSWORDS_DONT_MATCH": "Passwords do not match",
    "INVALID_PHONE": "Please enter a valid phone number",
    "INVALID_URL": "Please enter a valid URL",
    "MIN_LENGTH": "Minimum length is {{min}} characters",
    "MAX_LENGTH": "Maximum length is {{max}} characters",
    "INVALID_NUMBER": "Please enter a valid number",
    "INVALID_DATE": "Please enter a valid date",
    "INVALID_FORMAT": "Please enter a valid format",
    "LOADING": "Loading...",
    "ITEMS_SELECTED": "{{count}} items selected",
    "SEARCH": "Search...",
    "NO_OPTIONS_FOUND": "No options found"
  },
  "FORM_LABELS": {
    "TEXT_INPUT": "Required Text Input",
    "EMAIL": "Email Address",
    "PASSWORD": "Password",
    "CONFIRM_PASSWORD": "Confirm Password",
    "PHONE": "Phone Number",
    "URL": "Website URL",
    "NUMBER": "Number Input",
    "INTEGER_NUMBER": "Integer Number",
    "DECIMAL_NUMBER": "Decimal Number",
    "TEXTAREA": "Message",
    "TEXTAREA_DESCRIPTION": "Description (Max 100 characters)",
    "CHECKBOX": "I agree to the terms",
    "CHECKBOX_SM": "Small Checkbox",
    "CHECKBOX_MD": "Medium Checkbox",
    "CHECKBOX_LG": "Large Checkbox (Required)",
    "RADIO_OPTION": "Option",
    "RADIO_OPTION_1": "Option 1",
    "RADIO_OPTION_2": "Option 2",
    "RADIO_OPTION_3": "Option 3",
    "SWITCH": "Enable notifications",
    "SWITCH_SM": "Small Switch (Default OFF)",
    "SWITCH_MD": "Medium Switch (Default ON)",
    "SWITCH_LG": "Large Switch (Required)",
    "DATE": "Select Date",
    "DATE_RANGE": "Date Range",
    "DATE_RANGE_PICKER": "Date Range Picker",
    "SINGLE_IMAGE": "Profile Picture",
    "MULTIPLE_IMAGES": "Gallery Images",
    "DISABLED_FIELD": "Disabled Input",
    "READONLY_FIELD": "Readonly Input",
    "SINGLE_SELECT": "Single Select Dropdown",
    "MULTI_SELECT": "Multi-Select Dropdown",
    "SMALL_DROPDOWN": "Small Dropdown",
    "MEDIUM_DROPDOWN": "Medium Dropdown",
    "LARGE_DROPDOWN": "Large Dropdown",
    "SEARCHABLE_DROPDOWN": "Searchable Dropdown",
    "RICH_DROPDOWN": "Rich Dropdown with Details",
    "GROUPED_SINGLE": "Grouped Single Select",
    "GROUPED_MULTI": "Grouped Multi-Select",
    "DISABLED_DROPDOWN": "Disabled Dropdown",
    "LOADING_DROPDOWN": "Loading Dropdown",
    "ERROR_DROPDOWN": "Error Dropdown",
    "SEARCH_INPUT": "Search",
    "SEARCH_WITH_VOICE": "Search with Voice"
  },
  "PLACEHOLDERS": {
    "ENTER_TEXT": "Enter some text",
    "ENTER_EMAIL": "<EMAIL>",
    "ENTER_PASSWORD": "Enter your password",
    "CONFIRM_PASSWORD": "Confirm your password",
    "ENTER_PHONE": "+966 50 123 4567",
    "ENTER_URL": "https://example.com",
    "ENTER_NUMBER": "Enter a number",
    "ENTER_DECIMAL": "Enter a decimal",
    "ENTER_MESSAGE": "Type your message here...",
    "ENTER_DESCRIPTION": "Enter a description...",
    "SELECT_DATE": "Select a date",
    "SELECT_DATE_RANGE": "Select date range",
    "SELECT_OPTION": "Select an option",
    "SELECT_MULTIPLE": "Select multiple options",
    "SEARCH_AND_SELECT": "Search and select",
    "SELECT_WITH_DETAILS": "Select with details",
    "SELECT_FROM_GROUPS": "Select from groups",
    "SELECT_MULTIPLE_GROUPS": "Select multiple from groups",
    "DISABLED_STATE": "Disabled state",
    "LOADING_STATE": "Loading...",
    "SELECT_REQUIRED": "Please select an option",
    "SEARCH_PLACEHOLDER": "Search...",
    "VOICE_SEARCH_PLACEHOLDER": "Click to start voice search"
  },
  "DESCRIPTIONS": {
    "RADIO_OPTION_1": "This is the first option with additional description text.",
    "RADIO_OPTION_2": "This is the second option with medium size radio button.",
    "RADIO_OPTION_3": "This is the third option with large size radio button.",
    "SWITCH_SM": "This is a small switch component that starts in the OFF state.",
    "SWITCH_MD": "This is a medium switch component that starts in the ON state.",
    "SWITCH_LG": "This is a large switch component with required validation.",
    "CHECKBOX_SM": "When a selection needs a further detailed explanation, it goes here.",
    "CHECKBOX_MD": "This is a medium-sized checkbox with additional context information.",
    "CHECKBOX_LG": "This large checkbox demonstrates the error state when required but not selected."
  },
  "SECTIONS": {
    "TEXT_INPUTS": "Text Inputs",
    "NUMBER_INPUTS": "Number Inputs with Increment/Decrement Buttons",
    "RADIO_BUTTONS": "Radio Buttons",
    "SWITCH_TOGGLE": "Switch/Toggle Components",
    "DATE_PICKERS": "Date Pickers",
    "TEXTAREA_SECTION": "Textarea",
    "CHECKBOXES": "Checkboxes",
    "DROPDOWNS": "Enhanced Dropdown Components",
    "SEARCH_INPUTS": "Search Inputs",
    "IMAGE_UPLOAD": "Image Upload Components",
    "SINGLE_IMAGE_UPLOAD": "Single Image Upload",
    "MULTIPLE_IMAGE_UPLOAD": "Multiple Image Upload",
    "SPECIAL_STATES": "Special States"
  },
  "SUBSECTIONS": {
    "BASIC_DROPDOWNS": "Basic Dropdowns",
    "SIZE_VARIATIONS": "Size Variations",
    "ADVANCED_FEATURES": "Advanced Features",
    "GROUPED_OPTIONS": "Grouped Options",
    "STATES_VALIDATION": "States and Validation"
  },
  "IMG_UPLOAD": {
    "UPLOAD_FILES": "Upload files",
    "FILE_INFO": "Maximum file size allowed is {{maxSize}}, supported file formats include {{allowedTypes}}.",
    "BROWSE_FILES": "Browse Files",
    "DRAG_DROP_MULTIPLE": "Drag and drop files here to upload",
    "UPLOADING": "Uploading... {{progress}}%",
    "INVALID_FILE_TYPE": "Invalid file type. Allowed types: {{allowedTypes}}",
    "FILE_SIZE_EXCEEDED": "File size exceeds limit. Maximum size: {{maxSize}}"
  },
  "TREE": {
    "TREE_NAVIGATION": "Tree navigation",
    "EXPAND": "Expand",
    "COLLAPSE": "Collapse",
    "NO_ITEMS": "No items to display",
    "ADD_ITEMS": "Add items to see them here",
    "TREE_COMPONENT_EXAMPLES": "Tree Component Examples",
    "HIERARCHICAL_TREE": "Hierarchical tree structure with collapsible layers following SDGA design system",
    "LAST_ACTION": "Last Action",
    "SELECTED_NODE_ID": "Selected Node ID",
    "BASIC_TREE": "Basic Tree",
    "STANDARD_TREE": "Standard tree with multiple expansion and icon controls",
    "ARABIC_TREE_RTL": "Arabic Tree (RTL)",
    "TREE_ARABIC_CONTENT": "Tree with Arabic content and RTL support",
    "SINGLE_EXPANSION_MODE": "Single Expansion Mode",
    "SINGLE_EXPANSION_DESC": "Only one node can be expanded at a time, click anywhere to expand",
    "AUTO_EXPANDED_TREE": "Auto-Expanded Tree",
    "AUTO_EXPANDED_DESC": "Tree with initial expansion up to level 2",
    "COMPLEX_TREE_ICONS": "Complex Tree with Icons",
    "COMPLEX_TREE_DESC": "Tree with custom icons, badges, and disabled states",
    "MINIMAL_TREE": "Minimal Tree",
    "MINIMAL_TREE_DESC": "Clean tree without icons or connectors, click anywhere to expand",
    "CONFIGURATION_OPTIONS": "Configuration Options",
    "BASIC_CONFIGURATION": "Basic Configuration",
    "SINGLE_EXPANSION": "Single Expansion",
    "MINIMAL_CONFIGURATION": "Minimal Configuration",
    "USAGE_EXAMPLE": "Usage Example",
    "DATA_STRUCTURE": "Data Structure"
  },
  "TREE_EXAMPLE": {
    "PAGE_SECTION": "Page Section",
    "NESTED_PAGE_SECTION": "Nested Page Section",
    "PAGE_SECTION_DESC": "Main page section with subsections",
    "PAGE_SECTION_CONTENT": "This is the main page section containing various subsections and content areas.",
    "NESTED_SECTION_DESC": "Nested section with detailed content",
    "NESTED_SECTION_CONTENT": "This nested section contains detailed information and additional subsections.",
    "DEEP_NESTED_DESC": "Deep nested content",
    "DEEP_NESTED_CONTENT": "This is a deeply nested section with specific content for this area.",
    "ANOTHER_DEEP_DESC": "Another deep nested section",
    "ANOTHER_DEEP_CONTENT": "Another deeply nested section with different content and information.",
    "SECONDARY_DESC": "Secondary section",
    "SECONDARY_CONTENT": "This is a secondary section with its own unique content and purpose.",
    "ANOTHER_MAIN_DESC": "Another main section",
    "ANOTHER_MAIN_CONTENT": "This is another main section with different content and structure.",
    "FIRST_NESTED_DESC": "First nested item",
    "FIRST_NESTED_CONTENT": "Content for the first nested item in this section.",
    "SECOND_NESTED_DESC": "Second nested item",
    "SECOND_NESTED_CONTENT": "Content for the second nested item with different information.",
    "COMPLEX_NESTED_DESC": "Complex nested section",
    "COMPLEX_NESTED_CONTENT": "This is a complex nested section with multiple subsections.",
    "SUB_ITEM_ONE_DESC": "Sub-item one",
    "SUB_ITEM_ONE_CONTENT": "Detailed content for the first sub-item.",
    "SUB_ITEM_TWO_DESC": "Sub-item two",
    "SUB_ITEM_TWO_CONTENT": "Detailed content for the second sub-item.",
    "STANDALONE_DESC": "Standalone section",
    "STANDALONE_CONTENT": "This is a standalone section without children but with rich content.",
    "EXTERNAL_LINK_DESC": "External link section",
    "EXTERNAL_LINK_CONTENT": "This section contains information about external resources and links.",
    "FINAL_DESC": "Final section",
    "FINAL_CONTENT": "This is the final section with concluding information and resources.",
    "GOVERNMENT_SERVICES": "Government Services",
    "DIGITAL_SERVICES": "Digital Services",
    "ONLINE_APPLICATIONS": "Online Applications",
    "DOCUMENT_VERIFICATION": "Document Verification",
    "TRADITIONAL_SERVICES": "Traditional Services",
    "IN_PERSON_SERVICES": "In-Person Services",
    "PHONE_SERVICES": "Phone Services",
    "MINISTRY_DEPARTMENTS": "Ministry Departments",
    "HUMAN_RESOURCES": "Human Resources",
    "INFORMATION_TECHNOLOGY": "Information Technology",
    "PUBLIC_RELATIONS": "Public Relations",
    "SUPPORT_CENTER": "Support Center",
    "TECHNICAL_SUPPORT": "Technical Support",
    "CUSTOMER_SERVICE": "Customer Service",
    "DOCUMENTS": "Documents",
    "DOCUMENTS_DESC": "Document storage folder",
    "DOCUMENTS_CONTENT": "This folder contains all project documents, reports, and important files.",
    "PROJECTS": "Projects",
    "PROJECTS_DESC": "Active project files",
    "PROJECTS_CONTENT": "Contains all active project files including specifications, designs, and deliverables.",
    "PROJECT_A_PDF": "Project A.pdf",
    "PROJECT_A_DESC": "Project A documentation",
    "PROJECT_A_CONTENT": "Complete documentation for Project A including requirements, design, and implementation details.",
    "PROJECT_B_DOCX": "Project B.docx",
    "PROJECT_B_DESC": "Project B specifications",
    "PROJECT_B_CONTENT": "Detailed specifications and requirements for Project B development.",
    "PROJECT_C_XLSX": "Project C.xlsx",
    "PROJECT_C_DESC": "Project C budget and timeline",
    "PROJECT_C_CONTENT": "Budget breakdown and timeline for Project C implementation.",
    "REPORTS": "Reports",
    "REPORTS_DESC": "Generated reports",
    "REPORTS_CONTENT": "Collection of generated reports including annual summaries and monthly updates.",
    "ANNUAL_REPORT_PDF": "Annual Report.pdf",
    "ANNUAL_REPORT_DESC": "Yearly performance report",
    "ANNUAL_REPORT_CONTENT": "Comprehensive annual report covering all activities, achievements, and financial performance.",
    "MONTHLY_SUMMARY_XLSX": "Monthly Summary.xlsx",
    "MONTHLY_SUMMARY_DESC": "Monthly activity summary",
    "MONTHLY_SUMMARY_CONTENT": "Monthly summary of activities, metrics, and key performance indicators.",
    "README_TXT": "README.txt",
    "README_DESC": "Important instructions",
    "README_CONTENT": "Important instructions and guidelines for using the document management system.",
    "IMAGES": "Images",
    "IMAGES_DESC": "Image assets folder",
    "IMAGES_CONTENT": "Collection of image assets including logos, banners, and icons used across the platform.",
    "LOGO_PNG": "logo.png",
    "LOGO_DESC": "Official logo",
    "LOGO_CONTENT": "Official organization logo in PNG format with transparent background.",
    "BANNER_JPG": "banner.jpg",
    "BANNER_DESC": "Website banner",
    "BANNER_CONTENT": "Main website banner image used on the homepage and marketing materials.",
    "ICON_SVG": "icon.svg",
    "ICON_DESC": "Scalable icon",
    "ICON_CONTENT": "Scalable vector icon used in various sizes throughout the application.",
    "ARCHIVE": "Archive",
    "ARCHIVE_DESC": "Archived files (disabled)",
    "ARCHIVE_CONTENT": "This folder contains archived files that are no longer actively used but kept for reference.",
    "MIXED_LANGUAGE_TREE": "Government Services Tree",
    "MIXED_LANGUAGE_DESC": "Tree with dynamic Arabic/English content based on current language",
    "USER_FORM": "User Registration Form",
    "USER_FORM_DESC": "Example form with shared content template",
    "TITLE": "Tree Component Example",
    "DESCRIPTION": "Simple and reusable tree component with content sharing using ng-template",
    "SIMPLE_TREE": "Simple Tree with Content Sharing",
    "SIMPLE_TREE_DESC": "Click on any node to see its shared content displayed below",
    "STEP_ONE": "Step One",
    "STEP_ONE_DESC": "Simple text content",
    "STEP_TWO": "Step Two",
    "STEP_TWO_DESC": "Rich content with components",
    "FORMS_SECTION": "Forms Section",
    "FORMS_DESC": "Interactive forms",
    "USER_FORM": "User Registration",
    "USER_FORM_DESC": "User registration form",
    "EXTERNAL_LINK": "External Link",
    "EXTERNAL_LINK_DESC": "Link to external resource",
    "STEP_ONE_CONTENT_TITLE": "Simple Text Content",
    "STEP_ONE_CONTENT_DESC": "This demonstrates how to share simple text content using ng-template.",
    "STEP_ONE_CONTENT_TEXT": "This is a simple text description for Step One. You can include any HTML content, styling, and even Angular components within the template.",
    "STEP_TWO_CONTENT_TITLE": "Rich Content with Progress Bars",
    "STEP_TWO_CONTENT_DESC": "This shows how to include interactive components and rich content.",
    "PROGRESS_TASK_1": "Task 1 - Design",
    "PROGRESS_TASK_2": "Task 2 - Development",
    "PROGRESS_TASK_3": "Task 3 - Testing",
    "FORM_CONTENT_TITLE": "Interactive User Registration Form",
    "FORM_CONTENT_DESC": "This demonstrates how to embed forms and interactive elements.",
    "TREE_TITLE": "Tree Title",
    "LOGIN_FORM": "Form for Login",
    "LOGIN_FORM_CONTENT": "Login Form Content",
    "LISTS": "Lists",
    "LIST_1": "List 1",
    "LIST_1_CONTENT": "List 1 Content",
    "LIST_2": "List 2",
    "LIST_2_CONTENT": "List 2 Content",
    "FREE_TEXT": "Free Text",
    "TEXT_1": "Text 1",
    "TEXT_1_CONTENT": "Text 1 Content",
    "TEXT_2": "Text 2",
    "TEXT_2_CONTENT": "Text 2 Content",
    "LOGIN_FORM_TITLE": "User Login Form",
    "USERNAME": "Username",
    "USERNAME_PLACEHOLDER": "Enter your username",
    "PASSWORD": "Password",
    "PASSWORD_PLACEHOLDER": "Enter your password",
    "REMEMBER_ME": "Remember me",
    "LOGIN": "Login",
    "LIST_1_TITLE": "Government Services List",
    "LIST_1_ITEM_1": "Digital Identity Verification",
    "LIST_1_ITEM_2": "Online Document Processing",
    "LIST_1_ITEM_3": "Electronic Payment Gateway",
    "LIST_1_ITEM_4": "Citizen Support Portal",
    "LIST_2_TITLE": "Department Categories",
    "LIST_2_CATEGORY_1": "Administrative Services",
    "LIST_2_ITEM_1": "License Applications",
    "LIST_2_ITEM_2": "Permit Requests",
    "LIST_2_CATEGORY_2": "Public Services",
    "LIST_2_ITEM_3": "Health Records",
    "LIST_2_ITEM_4": "Education Certificates",
    "TEXT_1_TITLE": "About Digital Government",
    "TEXT_1_PARAGRAPH_1": "The Saudi Digital Government Authority (SDGA) is committed to transforming government services through innovative digital solutions.",
    "TEXT_1_PARAGRAPH_2": "Our mission is to enhance citizen experience by providing seamless, efficient, and accessible digital services that meet the highest standards of quality and security.",
    "TEXT_1_QUOTE": "Digital transformation is not just about technology; it's about reimagining how government serves its citizens.",
    "TEXT_1_PARAGRAPH_3": "Through continuous innovation and collaboration, we strive to build a digital ecosystem that empowers citizens and businesses to thrive in the digital age.",
    "TEXT_2_TITLE": "Service Information",
    "TEXT_2_SECTION_1": "Service Availability",
    "TEXT_2_CONTENT_1": "All digital services are available 24/7 through our secure online platform, ensuring citizens can access government services at their convenience.",
    "TEXT_2_SECTION_2": "Security & Privacy",
    "TEXT_2_CONTENT_2": "We implement the highest security standards to protect citizen data and ensure privacy compliance with international best practices.",
    "TEXT_2_SECTION_3": "Support & Assistance",
    "TEXT_2_CONTENT_3": "Our dedicated support team is available to assist citizens with any questions or technical issues they may encounter.",
    "FIRST_NAME": "First Name",
    "LAST_NAME": "Last Name",
    "EMAIL": "Email Address",
    "CANCEL": "Cancel",
    "SUBMIT": "Submit"
  },
  "TREE_TEMPLATE": {
    "USER_PROFILE": "User Profile",
    "USER_PROFILE_DESC": "Interactive user profile with avatar and details",
    "PROJECT_DASHBOARD": "Project Dashboard",
    "PROJECT_DASHBOARD_DESC": "Interactive project statistics and progress",
    "DOCUMENT_MANAGEMENT": "Document Management",
    "DOCUMENT_MANAGEMENT_DESC": "Advanced document management with previews",
    "PROJECT_PROPOSAL": "Project Proposal.pdf",
    "PROJECT_PROPOSAL_DESC": "Comprehensive project proposal document",
    "TECHNICAL_SPECS": "Technical Specifications.docx",
    "TECHNICAL_SPECS_DESC": "Detailed technical specifications",
    "TEAM_INFORMATION": "Team Information",
    "TEAM_INFORMATION_DESC": "Detailed team member information",
    "TEMPLATE_TREE": "Template-Based Tree",
    "TEMPLATE_TREE_DESC": "Tree with custom ng-template content for rich interactive displays"
  },
  "TREE_DYNAMIC": {
    "USER_MANAGEMENT": "User Management",
    "USER_MANAGEMENT_DESC": "Interactive user management with forms",
    "USER_FORM": "User Registration Form",
    "USER_FORM_DESC": "Dynamic form for user registration",
    "USER_SETTINGS": "User Settings",
    "USER_SETTINGS_DESC": "User preferences and settings",
    "MEDIA_GALLERY": "Media Gallery",
    "MEDIA_GALLERY_DESC": "Interactive image and media gallery",
    "DATA_ANALYTICS": "Data Analytics",
    "DATA_ANALYTICS_DESC": "Interactive data tables and charts",
    "USER_DATA_TABLE": "User Data Table",
    "USER_DATA_TABLE_DESC": "Interactive data table with sorting and filtering",
    "PERFORMANCE_CHART": "Performance Chart",
    "PERFORMANCE_CHART_DESC": "Interactive performance visualization",
    "NOTIFICATION_CENTER": "Notification Center",
    "NOTIFICATION_CENTER_DESC": "Interactive notification management",
    "DYNAMIC_CONTENT_TREE": "Dynamic Content Tree",
    "DYNAMIC_CONTENT_DESC": "Tree with ngTemplateOutlet for dynamic content rendering"
  },
  "GLOBAL": {
    "CURRENT_LANGUAGE": "Current Language",
    "ALL_TREES_UPDATE": "All trees below will update automatically",
    "SAME_CONTENT_RTL": "Same content as basic tree but with RTL layout direction",
    "CLOSE_CONTENT": "Close content",
    "VISIT_LINK": "Visit Link",
    "JOINED": "Joined",
    "ACTIVE_PROJECTS": "Active Projects",
    "COMPLETED_TASKS": "Completed Tasks",
    "TEAM_LEAD": "Team Lead",
    "TEAM_MEMBERS": "Team Members",
    "TECHNOLOGIES": "Technologies",
    "START_DATE": "Start Date",
    "BUDGET": "Budget",
    "TEAM_SIZE": "Team Size",
    "PROGRESS": "Progress",
    "TOTAL_TASKS": "Total Tasks",
    "COMPLETED": "Completed",
    "IN_PROGRESS": "In Progress",
    "PENDING": "Pending",
    "BUDGET_OVERVIEW": "Budget Overview",
    "TOTAL_BUDGET": "Total Budget",
    "SPENT": "Spent",
    "REMAINING": "Remaining",
    "TIMELINE": "Timeline",
    "END_DATE": "End Date",
    "MEMBERS": "members",
    "PROJECT_MILESTONES": "Project Milestones",
    "AUTHOR": "Author",
    "SIZE": "Size",
    "PAGES": "Pages",
    "VERSION": "Version",
    "MODIFIED": "Modified",
    "TAGS": "Tags",
    "DOWNLOAD": "Download",
    "PREVIEW": "Preview",
    "CANCEL": "Cancel",
    "SUBMIT": "Submit",
    "VIEW": "View",
    "SELECT_OPTION": "Select an option",
    "ENABLE_NOTIFICATIONS": "Enable Notifications",
    "RECEIVE_SYSTEM_NOTIFICATIONS": "Receive system notifications",
    "EMAIL_ALERTS": "Email Alerts",
    "RECEIVE_EMAIL_NOTIFICATIONS": "Receive email notifications",
    "LANGUAGE": "Language",
    "THEME": "Theme",
    "LIGHT": "Light",
    "DARK": "Dark",
    "SAVE_SETTINGS": "Save Settings",
    "MARK_ALL_AS_READ": "Mark All as Read"
  },
  "LANGUAGE": {
    "SWITCH_TO": "Switch to {{language}}",
    "CURRENT": "Current language: {{language}}",
    "ENGLISH": "English",
    "ARABIC": "العربية"
  },
  "ACCESSIBILITY": {
    "INCREASE_VALUE": "Increase value",
    "DECREASE_VALUE": "Decrease value",
    "NUMBER_INPUT": "Number input field"
  },
  "TEST_PAGE": {
    "TITLE": "SDGA Form Input Component Test",
    "LANGUAGE_DEMO_TITLE": "Language Switching Demo",
    "LANGUAGE_DEMO_DESC": "Use the language switcher in the top-right corner to toggle between English and Arabic. All form inputs and messages will automatically translate.",
    "CURRENT_LANGUAGE": "Current Language:",
    "BASIC_INPUTS": "Basic Input Types",
    "SPECIAL_INPUTS": "Special Input Types",
    "IMAGE_UPLOAD": "Image Upload Components",
    "SINGLE_IMAGE_UPLOAD": "Single Image Upload",
    "MULTIPLE_IMAGE_UPLOAD": "Multiple Image Upload",
    "SPECIAL_STATES": "Special States",
    "FORM_ACTIONS": "Form Actions",
    "FORM_SUBMITTED": "Form submitted successfully!",
    "FORM_HAS_ERRORS": "Please fix the errors above."
  },
  "PROGRESS": {
    "STEP_ONE": "Step One",
    "STEP_TWO": "Step Two",
    "STEP_THREE": "Step Three",
    "STEP_ONE_DESC": "This is a simple text description for Step One.",
    "STEP_TWO_DESC": "Fill out the form below.",
    "STEP_THREE_DESC": "Review and confirm your information."
  },
  "LOCATION": {
    "RIYADH": "Riyadh",
    "JEDDAH": "Jeddah",
    "DAMMAM": "Dammam",
    "UNKNOWN": "Unknown",
    "CAIRO": "Cairo"
  },
  "WEATHER": {
    "Clear": "Clear",
    "Clouds": "Cloudy",
    "Rain": "Rainy",
    "Snow": "Snowy",
    "Thunderstorm": "Thunderstorm",
    "Drizzle": "Drizzle",
    "Mist": "Mist",
    "Unavailable": "Unavailable"
  },
  "CODE_SNIPPET": {
    "SHOW_LESS": "Show Less",
    "SHOW_MORE": "Show More",
    "NO_CODE": "No code available."
  },
  "EXAMPLES": {
    "EXAMPLES": "Examples",
    "TAILWIND_CLASSES": "Tailwind Classes",
    "SVG_ICONS": "SVG Icons",
    "PROGRESS_INDICATORS": "Progress Indicators",
    "INPUTS": "Inputs",
    "ACCORDION": "Accordion",
    "NOTIFICATIONS": "Notifications",
    "RATING": "Rating",
    "MODAL": "Modal",
    "QUOTE": "Quote",
    "CAROUSEL": "Carousel",
    "BUTTONS": "Buttons",
    "CONTENT_SWITCHER": "Content Switcher",
    "TAGES": "Tages",
    "MENU": "Menu",
    "PROGRESS_BAR": "Progress Bar",
    "TREE_EXAMPLE": "Tree Example",
    "CODE_SNIPPET": "Code Snippet",
    "TABLE_EXAMPLE": "Table Example"
  },
  "table": {
    "headers": {
      "avatar": "Avatar",
      "name": "Name",
      "email": "Email",
      "department": "Department",
      "position": "Position",
      "salary": "Salary",
      "status": "Status",
      "manager": "Manager",
      "actions": "Actions"
    },
    "actions": {
      "view": "View",
      "edit": "Edit",
      "delete": "Delete",
      "filter": "Filter"
    },
    "status": {
      "active": "Active",
      "inactive": "Inactive",
      "pending": "Pending"
    },
    "messages": {
      "no_data": "No data to display",
      "loading": "Loading...",
      "filter_applied": "Filter applied to column: {{column}}",
      "sort_applied": "Column sorted: {{column}} {{direction}}",
      "items_selected": "{{count}} items selected",
      "page_info": "Page {{current}} of {{total}}",
    "showing_items": "Showing {{start}}-{{end}} of {{total}} items"
    }
  },
  "NOT_FOUND": {
    "TITLE": "Something went wrong",
    "DESCRIPTION": "Sorry, we can't find the page you're looking for.",
    "BACK": "Back To Home"
  },
  "CONTACT_US": {
    "TITLE": "Contact us",
    "DESCRIPTION": "Service description example To buy a plot...",
    "FIRST_NAME": "First Name",
    "LAST_NAME": "Last Name",
    "EMAIL": "Email",
    "PHONE": "Phone",
    "SUBJECT": "Subject",
    "CATEGORY": "Category",
    "MESSAGE": "How Can We Help?",
    "UPLOAD_LABEL": "Upload files",
    "UPLOAD_NOTE": "Maximum file size allowed is 2MB. Supported file formats include .jpg, .png, and .pdf.",
    "SUBMIT": "Request permit",
    "PLACEHOLDER": {
      "FIRST_NAME": "Type your first name",
      "LAST_NAME": "Type your last name",
      "EMAIL": "Type your email",
      "PHONE": "00 000 0000",
      "SUBJECT": "Type your subject",
      "CATEGORY": "Suggestion",
      "MESSAGE": "Placeholder text"
    },
    "CONTACT_AMER": "Contact Amer",
    "UPLOAD_FILES": "Upload files",
    "LOCATION": "Location",
    "FAX":" Fax",
    "SMS":" SMS",
    "FOLLOW_US":" Follow Us",
    "EMERGENCY": "Emergency Numbers",
    "DEFENCE": "Civil Defence",
    "POLICE": "Police",
    "AMBULANCE": "Ambulance",
    "VIEW_MORE": "View More"
  },
  "PAGES": {
    "PAGES":"Pages"
  }
}
