import { Component, EventEmitter, Input, Output, TemplateRef, ViewChild, OnInit, OnDestroy, HostListener } from '@angular/core';
import {ColumnTypeEnum,DataHandlingType,} from '../../../core/enums/column-type-enum';
import { ActionDisplayMode, ITableColumn, SwitchToggleEvent, TableActionEvent, TextLinkClickEvent } from '../../../core/gl-interfaces/i-table';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
// Material UI imports removed - now using SDGA components only
import { FormInput } from '../inputs/form-input/form-input';
import { FormControl } from '@angular/forms';
import { Pagination, PageChangeEvent } from '../pagination/pagination';
import { FromDropdown, DropdownOption } from '../inputs/from-dropdown/from-dropdown';
import { SvgIcon } from '../svg-icon/svg-icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'sdga-table',
  imports: [
    CommonModule,
    FormInput,
    Pagination,
    SvgIcon,
    TranslateModule
  ],
  templateUrl: './table.html',
  styleUrl: './table.scss'
})
export class Table {
columnTypeEnum = ColumnTypeEnum;
  ActionDisplayMode=ActionDisplayMode

  constructor(private translateService: TranslateService) {}
  @Input() columns: ITableColumn[] | undefined;
  @Input() displayedColumns: string[] | undefined;
  @Input() sortingType: DataHandlingType | undefined;
  @Input() paginationType: DataHandlingType | undefined;
  @Input() totalItems = 0;
  @Input() pageSize = 10;
  @Input() currentPage = 1;

  @Input() selection = new SelectionModel<any>(true, []);
  @Input() dataSource: any | undefined;

  // Form controls for checkboxes, switches, and dropdowns
  selectAllControl = new FormControl(false);
  rowCheckboxControls: { [key: string]: FormControl } = {};
  switchControls: { [key: string]: FormControl } = {};
  actionDropdownControls: { [key: string]: FormControl } = {};

  // Custom dropdown state management
  openActionDropdowns: Set<string> = new Set();

  @Output() onClickAction = new EventEmitter<TableActionEvent>();
  @Output() switchToggleEvent = new EventEmitter<SwitchToggleEvent>();
  @Output() textLinkClick = new EventEmitter<TextLinkClickEvent>();
  @Output() toggleAllRows = new EventEmitter<void>();
  @Output() toggleRow = new EventEmitter<any>();

  @Output() sortChanged = new EventEmitter<{
    active: string;
    direction: string;
  }>();
  @Output() pageChange = new EventEmitter();
  @Output() filterClick = new EventEmitter<string>();

  @Input() customColumnTemplate!: TemplateRef<any>;

  // Sorting state
  currentSortColumn: string = '';
  currentSortDirection: 'asc' | 'desc' | '' = '';


  ngOnInit(): void {
  
  }

  ngAfterViewInit(): void {
    // No Material UI initialization needed
  }

  // Legacy method for backward compatibility
  onPageChange(event: any) {
    if (this.paginationType === DataHandlingType.Backend) {
      this.pageChange.emit(event);
    }
  }

  onSdgaPageChange(event: PageChangeEvent) {
    this.currentPage = event.page;

    // Always emit page change event for both frontend and backend pagination
    if (this.paginationType === DataHandlingType.Backend) {
      // Convert SDGA pagination event to Material UI format for backward compatibility
      const matEvent = {
        pageIndex: event.page - 1, // SDGA uses 1-based, Material uses 0-based
        pageSize: event.itemsPerPage,
        length: this.totalItems
      };
      this.pageChange.emit(matEvent);
    } else {
      // For frontend pagination, emit the SDGA event directly
      this.pageChange.emit(event);
    }
  }

  //  initializePagination(): void {
  //   if (this.paginationType === DataHandlingType.Backend) {
  //     this.paginator.page.subscribe((event: PageEvent) => {
  //       this.pageChange.emit(event); // Emit backend pagination parameters
  //     });
  //   }
  // }

  isAllSelected(): boolean {
    if (!this.dataSource) return false;
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return this.isAllSelected() ? 'deselect all' : 'select all';
    }
    return this.selection.isSelected(row)
      ? `deselect row ${row.position + 1}`
      : `select row ${row.position + 1}`;
  }

  onToggleAllRows(): void {
    this.toggleAllRows.emit();
  }

  onToggleRow(row: any): void {
    this.toggleRow.emit(row);
  }

  handleAction(action: string, row: any): void {
    debugger
    this.onClickAction.emit({ action, row });
  }

  onToggle(row: any, newValue: boolean): void {
    this.switchToggleEvent.emit({ row, newValue });
  }

  onTextLinkClick(row: any, columnDef: string): void {
    this.textLinkClick.emit({ row, columnDef });
  }

  getRowCheckboxControl(row: any): FormControl {
    const rowId = row.id || JSON.stringify(row);
    if (!this.rowCheckboxControls[rowId]) {
      this.rowCheckboxControls[rowId] = new FormControl(this.selection.isSelected(row));
    }
    return this.rowCheckboxControls[rowId];
  }

  getSwitchControl(row: any, columnDef: string): FormControl {
    const switchId = `${row.id || JSON.stringify(row)}_${columnDef}`;
    if (!this.switchControls[switchId]) {
      this.switchControls[switchId] = new FormControl(false);
    }
    return this.switchControls[switchId];
  }

  getActionDropdownControl(row: any, columnDef: string): FormControl {
    const dropdownId = `${row.id || JSON.stringify(row)}_${columnDef}_actions`;
    if (!this.actionDropdownControls[dropdownId]) {
      this.actionDropdownControls[dropdownId] = new FormControl(null);
    }
    return this.actionDropdownControls[dropdownId];
  }

  getActionDropdownOptions(row: any, column: ITableColumn): DropdownOption[] {
    const buttons = column.cell(row)?.buttons || [];
    return buttons.map((button: any) => ({
      value: button.action,
      label: button.label,
      disabled: button.disabled || false
    }));
  }

  handleDropdownAction(selectedAction: any, row: any): void {
    if (selectedAction) {
      this.handleAction(selectedAction, row);
    }
  }

  trackByFn(index: number, item: any): any {
    return item.id || index;
  }

  onSort(columnDef: string): void {
    // Toggle sort direction
    if (this.currentSortColumn === columnDef) {
      this.currentSortDirection = this.currentSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.currentSortColumn = columnDef;
      this.currentSortDirection = 'asc';
    }

    if (this.sortingType === DataHandlingType.Backend) {
      this.sortChanged.emit({
        active: this.currentSortColumn,
        direction: this.currentSortDirection
      });
    } else if (this.sortingType === DataHandlingType.Frontend) {
      // Implement frontend sorting if needed
      this.sortDataLocally(columnDef, this.currentSortDirection);
    }
  }

  onFilterClick(columnDef: string): void {
    this.filterClick.emit(columnDef);
  }

  getColumnHeader(column: ITableColumn): string {
    if (column.headerKey) {
      return this.translateService.instant(column.headerKey);
    }
    return column.header;
  }

  // Custom dropdown methods
  toggleActionDropdown(row: any): void {
    const rowId = this.getRowId(row);
    if (this.openActionDropdowns.has(rowId)) {
      this.openActionDropdowns.delete(rowId);
    } else {
      // Close all other dropdowns first
      this.openActionDropdowns.clear();
      this.openActionDropdowns.add(rowId);
    }
  }

  isActionDropdownOpen(row: any): boolean {
    const rowId = this.getRowId(row);
    return this.openActionDropdowns.has(rowId);
  }

  closeActionDropdown(row: any): void {
    const rowId = this.getRowId(row);
    this.openActionDropdowns.delete(rowId);
  }

  handleActionClick(action: string, row: any): void {
    this.handleAction(action, row);
    this.closeActionDropdown(row);
  }

  private getRowId(row: any): string {
    return row.id?.toString() || JSON.stringify(row);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    // Close all dropdowns when clicking outside
    const target = event.target as HTMLElement;
    if (!target.closest('.relative.inline-block')) {
      this.openActionDropdowns.clear();
    }
  }

  private sortDataLocally(columnDef: string, direction: 'asc' | 'desc'): void {
    if (this.dataSource && this.dataSource.data) {
      const column = this.columns?.find(col => col.columnDef === columnDef);
      if (column) {
        this.dataSource.data.sort((a: any, b: any) => {
          const aValue = column.cell(a);
          const bValue = column.cell(b);

          if (aValue < bValue) return direction === 'asc' ? -1 : 1;
          if (aValue > bValue) return direction === 'asc' ? 1 : -1;
          return 0;
        });
      }
    }
  }

  getStatusDotClass(statusObj: any): string {
    if (!statusObj) return 'bg-gray-400';

    // Extract status type from class or label
    const statusClass = statusObj.class || '';
    const statusLabel = statusObj.label || '';

    if (statusClass.includes('green') || statusLabel.includes('نشط') || statusLabel.includes('active')) {
      return 'bg-green-500';
    } else if (statusClass.includes('red') || statusLabel.includes('غير نشط') || statusLabel.includes('inactive')) {
      return 'bg-red-500';
    } else if (statusClass.includes('yellow') || statusLabel.includes('انتظار') || statusLabel.includes('pending')) {
      return 'bg-yellow-500';
    }

    return 'bg-gray-400';
  }
}

