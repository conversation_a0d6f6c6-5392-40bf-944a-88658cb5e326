import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';

@Component({
  selector: 'sdga-carousel',
  imports: [CommonModule, SvgIcon],
  templateUrl: './carousel.html',
  styleUrl: './carousel.scss',
})
export class Carousel {
  @Input() images: string[] = [''];
  @Input() btnStyle: string = '';
  leftArrow = 'white_leftArrow';
  rightArrow = 'white_rightArrow';
  iconClass = '';
  bulletClass = '';
  @Input() dotSize: string = '';
  currentIndex = 0;

  next() {
    this.currentIndex = (this.currentIndex + 1) % this.images.length;
  }

  prev() {
    this.currentIndex =
      (this.currentIndex - 1 + this.images.length) % this.images.length;
  }

  goToSlide(index: number) {
    this.currentIndex = index;
  }

  buttonStyle(): string {
    switch (this.btnStyle) {
      case 'lg sa-600':
        return 'bg-sa-600 w-12 h-12  px-3 ';
        break;
      case 'lg sa-600 border':
        return 'bg-sa-600 w-12 h-12  px-3 px-3 ';
        break;
      case 'lg sa-600-bordered':
        return 'bg-sa-600 w-12 h-12  px-3';
        break;
      case 'lg sa-700':
        return 'bg-sa-700 w-12 h-12 px-3 ';
        break;
      case 'lg sa-900':
        return 'bg-sa-900 w-12 h-12 px-3';
        break;
      case 'lg transparent':
        this.leftArrow = 'arrow-left';
        this.rightArrow = 'arrow-right';
        this.iconClass = 'text-default';
        return 'bg-transparent w-12 h-12  px-3 border border-default-100';
        break;
      case 'md sa-600':
        return 'bg-sa-600 w-10 h-10 p-2 ';
        break;
      case 'md sa-600-bordered':
        return 'bg-sa-600 w-10 h-10 p-2';
        break;
      case 'md sa-700':
        return 'bg-sa-700 w-10 h-10 p-2';
        break;
      case 'md sa-900':
        return 'bg-sa-900 w-10 h-10 p-2';
        break;
      case 'md transparent':
        this.leftArrow = 'arrow-left';
        this.rightArrow = 'arrow-right';
        this.iconClass = 'text-default';
        return 'bg-transparent w-10 h-10  px-3 border border-default-100';
        break;
      case 'sm sa-600':
        return 'bg-sa-600 w-8 h-8 px-1';
        break;
      case 'sm sa-600-bordered':
        return 'bg-sa-600 w-8 h-8 px-1';
        break;
      case 'sm sa-700':
        return 'bg-sa-700 w-8 h-8 px-1';
        break;
      case 'sm sa-900':
        return 'bg-sa-900 w-8 h-8 px-1';
        break;
      case 'sm transparent':
        this.leftArrow = 'arrow-left';
        this.rightArrow = 'arrow-right';
        this.iconClass = 'text-default';
        return 'bg-transparent w-10 h-10  px-3 border border-default-100';
        break;
      default:
        return '';
    }
  }
}
