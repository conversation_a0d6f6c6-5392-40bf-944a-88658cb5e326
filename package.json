{"name": "sdga-theme", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.3", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "prismjs": "^1.30.0", "rxjs": "~7.8.0", "tailwind-merge": "^3.3.1", "tailwindcss-rtl": "^0.9.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.0", "@tailwindcss/postcss": "^4.1.10", "@types/jasmine": "~5.1.0", "@types/prismjs": "^1.26.5", "autoprefixer": "^10.4.21", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "~5.8.2"}, "description": "This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 20.0.2.", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}