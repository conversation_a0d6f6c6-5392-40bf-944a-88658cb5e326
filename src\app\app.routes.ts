import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./layouts/default-layout/default-layout').then(
        (m) => m.DefaultLayout
      ),
    children: [
      {
        path: 'examples',
        data: { breadcrumb: 'EXAMPLES.EXAMPLES' },
        children: [
          {
            path: 'tailwind-calsses',
            data: { breadcrumb: 'EXAMPLES.TAILWIND_CLASSES' },
            loadComponent: () =>
              import('./examples/tailwind-classes-test/tailwind-classes-test').then(
                (m) => m.TailwindClassesTest
              ),
          },
          {
            path: 'svg-icons',
            data: { breadcrumb: 'EXAMPLES.SVG_ICONS' },
            loadComponent: () =>
              import('./examples/svg-icon-test/svg-icon-test').then(
                (m) => m.SvgIconTest
              ),
          },
          {
            path: 'progress-indicators',
            data: { breadcrumb: 'EXAMPLES.PROGRESS_INDICATORS' },
            loadComponent: () =>
              import('./examples/progress-indicator-test/progress-indicator-test').then(
                (m) => m.ProgressIndicatorTest
              ),
          },
          {
            path: 'inputs',
            data: { breadcrumb: 'EXAMPLES.INPUTS' },
            loadComponent: () =>
              import('./examples/inputs-test/inputs-test').then(
                (m) => m.InputsTest
              ),
          },
          {
            path: 'accordion',
            data: { breadcrumb: 'EXAMPLES.ACCORDION' },
            loadComponent: () =>
              import('./examples/accordion-test/accordion-test').then(
                (m) => m.AccordionTest
              ),
          },
          {
            path: 'notifications',
            data: { breadcrumb: 'EXAMPLES.NOTIFICATIONS' },
            loadComponent: () =>
              import('./examples/notification-toast-test/notification-toast-test').then(
                (m) => m.NotificationToastTest
              ),
          },
          {
            path: 'rating',
            data: { breadcrumb: 'EXAMPLES.RATING' },
            loadComponent: () =>
              import('./examples/rating-test/rating-test').then(
                (m) => m.RatingTest
              ),
          },
          {
            path: 'modal',
            data: { breadcrumb: 'EXAMPLES.MODAL' },
            loadComponent: () =>
              import('./examples/modal-test/modal-test').then(
                (m) => m.ModalTest
              ),
          },
          {
            path: 'quote',
            data: { breadcrumb: 'EXAMPLES.QUOTE' },
            loadComponent: () =>
              import('./examples/quote-test/quote-test').then(
                (m) => m.QuoteTest
              ),
          },
          {
            path: 'carousel',
            data: { breadcrumb: 'EXAMPLES.CAROUSEL' },
            loadComponent: () =>
              import('./examples/carousel-test/carousel-test').then(
                (m) => m.CarouselTest
              ),
          },
          {
            path: 'buttons',
            data: { breadcrumb: 'EXAMPLES.BUTTONS' },
            loadComponent: () =>
              import('./examples/buttons-test/buttons-test').then(
                (m) => m.ButtonsTest
              ),
          },
          {
            path: 'content-switcher',
            data: { breadcrumb: 'EXAMPLES.CONTENT_SWITCHER' },
            loadComponent: () =>
              import('./examples/content-switcher-test/content-switcher-test').then(
                (m) => m.ContentSwitcherTest
              ),
          },
          {
            path: 'tages',
            data: { breadcrumb: 'EXAMPLES.TAGES' },
            loadComponent: () =>
              import('./examples/tages-test/tages-test').then(
                (m) => m.TagesTest
              ),
          },
          {
            path: 'menu',
            data: { breadcrumb: 'EXAMPLES.MENU' },
            loadComponent: () =>
              import('./examples/menu-test/menu-test').then(
                (m) => m.MenuTest
              ),
          },
          {
            path: 'progress-bar',
            data: { breadcrumb: 'EXAMPLES.PROGRESS_BAR' },
            loadComponent: () =>
              import('./examples/progress-bar-test/progress-bar-test').then(
                (m) => m.ProgressBarTest
              ),
          },
          {
            path: 'tree-example',
            data: { breadcrumb: 'EXAMPLES.TREE_EXAMPLE' },
            loadComponent: () =>
              import('./examples/tree-example/tree-example').then(
                (m) => m.TreeExample
              ),
          },
          {
             path: 'filtration',
            data: { breadcrumb: 'Filtration' },
            loadComponent: () =>
              import('./examples/filtration-test/filtration-test').then(
                (m) => m.FiltrationTest
              ),
          },
          {
             path: 'avatar',
            data: { breadcrumb: 'Avatar' },
            loadComponent: () =>
              import('./examples/avatar-test/avatar-test').then(
                (m) => m.AvatarTest
              ),
          },
          {
            path: 'code-snippet',
            data: { breadcrumb: 'EXAMPLES.CODE_SNIPPET' },
            loadComponent: () =>
              import('./examples/code-snippets-test/code-snippets-test').then(
                (m) => m.CodeSnippetsTest
              ),
          },
          {
            path: 'table-example',
            data: { breadcrumb: 'Table Example' },
            loadComponent: () =>
              import('./examples/table-example/table-example').then(
                (m) => m.TableExample
              ),
          }
        ],
      },
      {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
      },
      {
        path: 'not-found',
        loadComponent: () =>
          import('./pages/not-found/not-found').then(m => m.NotFound)
      },
      {
        path: 'contact-us',
        data: { breadcrumb: 'CONTACT_US.TITLE' },
        loadComponent: () =>
          import('./pages/contact-us/contact-us').then((m) => m.ContactUs),
      }
    ],
  },

];
